from .escritorio import db
from sqlalchemy.sql import func

class Cliente(db.Model):
    __tablename__ = 'cliente'
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.In<PERSON>ger, db.<PERSON>ey('escritorio.id'), nullable=False)
    cnpj = db.Column(db.String(18), nullable=False)
    razao_social = db.Column(db.String(255), nullable=False)
    inscricao_estadual = db.Column(db.String(30))
    logradouro = db.Column(db.String(255))
    numero = db.Column(db.String(20))
    bairro = db.Column(db.String(100))
    municipio = db.Column(db.String(100))
    uf = db.Column(db.String(2))
    cep = db.Column(db.String(10))
    pais = db.Column(db.String(50))
    codigo_pais = db.Column(db.String(10))
    cnae = db.Column(db.String(20))
    atividade = db.Column(db.String(50))
    destinacao = db.Column(db.String(50))
    natureza_juridica = db.Column(db.String(50))
    simples_nacional = db.Column(db.Boolean, default=False)
    ind_ie_dest = db.Column(db.String(2))
    ind_final = db.Column(db.String(2))
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    status = db.Column(db.String(20), default='novo')

    # Relacionamentos
    tributos = db.relationship('Tributo', backref='cliente', lazy=True)

    def __repr__(self):
        return f"<Cliente {self.razao_social}>"

    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cnpj': self.cnpj,
            'razao_social': self.razao_social,
            'inscricao_estadual': self.inscricao_estadual,
            'logradouro': self.logradouro,
            'numero': self.numero,
            'bairro': self.bairro,
            'municipio': self.municipio,
            'uf': self.uf,
            'cep': self.cep,
            'pais': self.pais,
            'codigo_pais': self.codigo_pais,
            'cnae': self.cnae,
            'atividade': self.atividade,
            'destinacao': self.destinacao,
            'natureza_juridica': self.natureza_juridica,
            'simples_nacional': self.simples_nacional,
            'ind_ie_dest': self.ind_ie_dest,
            'ind_final': self.ind_final,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None,
            'status': self.status
        }
