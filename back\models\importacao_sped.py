from .escritorio import db
from sqlalchemy.sql import func


class ImportacaoSped(db.Model):
    __tablename__ = 'importacao_sped'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON>teger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.Foreign<PERSON>ey('escritorio.id'), nullable=False)
    usuario_id = db.Column(db.Integer, db.<PERSON>ey('usuario.id'), nullable=False)
    arquivo_nome = db.Column(db.String(255), nullable=False)
    cnpj_empresa = db.Column(db.String(18))
    razao_social_empresa = db.Column(db.String(255))
    data_inicio = db.Column(db.Date)  # DT_INI da TAG 0000
    data_fim = db.Column(db.Date)  # DT_FIN da TAG 0000
    total_notas = db.Column(db.Integer, default=0)
    total_itens = db.Column(db.Integer, default=0)
    total_clientes = db.Column(db.Integer, default=0)
    total_produtos = db.Column(db.Integer, default=0)
    data_importacao = db.Column(db.DateTime, server_default=func.now())
    status = db.Column(db.String(20), default='concluido')  # 'concluido', 'erro'
    mensagem = db.Column(db.Text)
    
    def __repr__(self):
        return f"<ImportacaoSped {self.id} - {self.arquivo_nome}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'usuario_id': self.usuario_id,
            'arquivo_nome': self.arquivo_nome,
            'cnpj_empresa': self.cnpj_empresa,
            'razao_social_empresa': self.razao_social_empresa,
            'data_inicio': self.data_inicio.isoformat() if self.data_inicio else None,
            'data_fim': self.data_fim.isoformat() if self.data_fim else None,
            'total_notas': self.total_notas,
            'total_itens': self.total_itens,
            'total_clientes': self.total_clientes,
            'total_produtos': self.total_produtos,
            'data_importacao': self.data_importacao.isoformat() if self.data_importacao else None,
            'status': self.status,
            'mensagem': self.mensagem
        }
