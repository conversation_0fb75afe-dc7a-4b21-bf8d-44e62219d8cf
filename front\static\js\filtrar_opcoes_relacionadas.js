/**
 * Filtra opções relacionadas com base em um filtro selecionado
 * @param {string} targetType - Tipo de opção a ser filtrada (ex: 'ncm')
 * @param {string} filterType - Tipo do filtro selecionado (ex: 'cfop')
 * @param {string} filterValue - Valor do filtro selecionado
 */
function filtrarOpcoesRelacionadas(targetType, filterType, filterValue) {
  console.log(`Filtrando ${targetType} por ${filterType} = ${filterValue}`);
  
  const checkboxes = document.querySelectorAll(`.filtro-checkbox[data-type="${targetType}"]`);
  
  // Se estivermos filtrando NCMs por CFOP
  if (filterType === 'cfop' && targetType === 'ncm') {
    // Obter NCMs relacionados ao CFOP selecionado
    const ncmsRelacionados = cfopNcmRelations.get(filterValue) || new Set();
    console.log(`NCMs relacionados ao CFOP ${filterValue}:`, Array.from(ncmsRelacionados));
    
    checkboxes.forEach(checkbox => {
      const ncmValue = checkbox.value;
      // Mostrar apenas os NCMs que estão relacionados ao CFOP selecionado
      const shouldShow = ncmsRelacionados.has(ncmValue);
      checkbox.closest('.form-check').style.display = shouldShow ? 'block' : 'none';
      
      // Se o NCM não estiver relacionado, desmarcar
      if (!shouldShow) {
        checkbox.checked = false;
      }
    });
  } else {
    // Comportamento padrão para outros tipos de relacionamentos
    checkboxes.forEach(checkbox => {
      const relatedValue = checkbox.dataset[filterType];
      // Se o checkbox tem um valor relacionado ao filtro, mostrar/ocultar com base na correspondência
      if (relatedValue) {
        const shouldShow = relatedValue === filterValue;
        checkbox.closest('.form-check').style.display = shouldShow ? 'block' : 'none';
        
        // Se estiver ocultando, desmarcar o checkbox
        if (!shouldShow) {
          checkbox.checked = false;
        }
      }
    });
  }
  
  // Se estivemos filtrando NCMs, rolar para o topo da lista
  if (targetType === 'ncm') {
    const container = document.getElementById('ncm-container');
    if (container) {
      const optionsList = container.querySelector('.filter-options-list');
      if (optionsList) {
        optionsList.scrollTop = 0;
      }
    }
  }
}
