import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database connection details
db_name = os.getenv("DB_NAME")
db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT")

# Tables to drop
tables_to_drop = [
    "empresa",
    "escritorio",
    "cliente",
    "importacao_xml",
    "log_atividade",
    "produto",
    "tributo_historico",
    "produto_backup",
    "tributo",
    "tributo_historico",
    "cenario_icms",
    "cenario_icms_st",
    "cenario_ipi",
    "cenario_pis",
    "cenario_cofins",
    "cenario_difal",
    "nota_fiscal_item",
    "auditoria_resultado",
    "auditoria_status_manual",
    "auditoria_sumario",
    "usuario"

]

conn = None
cur = None

try:
    # Establish database connection
    print(f"Connecting to database '{db_name}' on {db_host}:{db_port}...")
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    conn.autocommit = False  # Start transaction
    cur = conn.cursor()
    print("Connection successful.")

    # Drop tables
    for table in tables_to_drop:
        try:
            print(f"Attempting to drop table '{table}'...")
            # CASCADE will automatically drop dependent objects such as foreign keys
            sql = f'DROP TABLE IF EXISTS {table} CASCADE;'
            cur.execute(sql)
            print(f"Table '{table}' dropped successfully.")
        except psycopg2.Error as e:
            print(f"Error dropping table '{table}': {e}")
            raise  # Re-raise to trigger rollback

    # Commit transaction
    conn.commit()
    print("\nAll specified tables dropped successfully. Transaction committed.")

except psycopg2.OperationalError as e:
    print(f"\nDatabase connection error: {e}")
    if conn:
        conn.rollback()
except psycopg2.Error as e:
    print(f"\nAn error occurred during table dropping: {e}")
    if conn:
        print("Rolling back transaction.")
        conn.rollback()
except Exception as e:
    print(f"\nAn unexpected error occurred: {e}")
    if conn:
        conn.rollback()
finally:
    if cur:
        cur.close()
    if conn:
        conn.close()
        print("Database connection closed.")
