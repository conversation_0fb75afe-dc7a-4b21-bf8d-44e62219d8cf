"""
Serviço de IA para Chatbot do Sistema de Auditoria Fiscal
Integração com OpenAI GPT-4o-mini para responder perguntas sobre auditorias
"""

import os
import json
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import openai
from sqlalchemy import text
from models import db
from models.chatbot_conversas import ChatbotConversas
from models.chatbot_templates import ChatbotTemplates

class ChatbotIAService:
    def __init__(self):
        self.client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4o-mini')
        self.max_tokens = int(os.getenv('OPENAI_MAX_TOKENS', '1500'))
        self.temperature = float(os.getenv('OPENAI_TEMPERATURE', '0.3'))
        self.registro_empresa_em_andamento = False
        self.dados_empresa = {}
        self.campos_obrigatorios = ['cnpj', 'razao_social']
        self.campos_opcionais = [
            'nome_fantasia', 'inscricao_estadual', 'email', 'responsavel',
            'cep', 'logradouro', 'numero', 'complemento', 'bairro', 'cidade',
            'estado', 'cnae', 'tributacao', 'atividade', 'pis_cofins', 'observacoes'
        ]

    def processar_pergunta(self, pergunta: str, usuario_id: int, empresa_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Processa uma pergunta do usuário e retorna uma resposta inteligente
        """
        inicio = time.time()
        
        # Verificar se é uma solicitação para cadastrar empresa
        if not self.registro_empresa_em_andamento and any(palavra in pergunta.lower() for palavra in ['cadastrar empresa', 'nova empresa', 'registrar empresa']):
            return self._iniciar_registro_empresa(usuario_id)
        
        # Se estiver em andamento o registro de empresa, processar o próximo campo
        if self.registro_empresa_em_andamento:
            # Verificar se o usuário quer cancelar
            if pergunta.lower() in ['cancelar', 'parar', 'sair']:
                self.registro_empresa_em_andamento = False
                self.dados_empresa = {}
                return {
                    'resposta': 'Cadastro de empresa cancelado. Como posso ajudar?',
                    'dados': {}
                }
            
            # Obter o próximo campo a ser preenchido
            proximo_campo = None
            if 'dados' in self.dados_empresa and 'proximo_campo' in self.dados_empresa['dados']:
                proximo_campo = self.dados_empresa['dados']['proximo_campo']
            
            # Se não tiver próximo campo definido, iniciar do começo
            if not proximo_campo:
                return self._iniciar_registro_empresa(usuario_id)
            
            # Processar o campo atual
            resultado = self._processar_campo_empresa(pergunta, proximo_campo, usuario_id)
            
            # Se o próximo campo for None, significa que terminou o cadastro
            if 'proximo_campo' not in resultado or not resultado['proximo_campo']:
                self.registro_empresa_em_andamento = False
                return resultado
            
            # Atualizar o próximo campo
            if 'dados' not in resultado:
                resultado['dados'] = {}
            
            resultado['dados']['proximo_campo'] = resultado['proximo_campo']
            return resultado
        
        try:
            # 1. Analisar a pergunta e extrair entidades
            entidades = self._extrair_entidades(pergunta)

            # 2. Gerar consulta SQL baseada nas entidades e na pergunta
            sql, parametros = self._gerar_consulta_sql(pergunta, entidades, empresa_id)

            # 3. Executar a consulta no banco de dados
            dados = self._executar_consulta(sql, parametros)

            # 4. Gerar resposta natural usando IA
            resposta = self._gerar_resposta_ia(pergunta, dados, entidades)

            # 5. Calcular tempo de resposta
            tempo_resposta = int((time.time() - inicio) * 1000)

            # 5. Salvar conversa no histórico
            self._salvar_conversa(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=sql,
                dados_utilizados=dados,
                tempo_resposta=tempo_resposta
            )

            return {
                'success': True,
                'resposta': resposta,
                'dados': dados,
                'sql_utilizada': sql_query if os.getenv('FLASK_ENV') == 'development' else None,
                'tempo_resposta': tempo_resposta,
                'entidades_encontradas': entidades
            }

        except Exception as e:
            return {
                'success': False,
                'erro': str(e),
                'resposta': 'Desculpe, não consegui processar sua pergunta. Pode tentar reformular?'
            }

    def _extrair_entidades(self, pergunta: str) -> Dict[str, Any]:
        """
        Extrai entidades da pergunta (números de nota, nomes de empresa, tipos de tributo, etc.)
        """
        entidades = {
            'numeros_nota': [],
            'empresas': [],
            'tributos': [],
            'produtos': [],
            'clientes': [],
            'anos': [],
            'meses': [],
            'valores_monetarios': [],
            'percentuais': [],
            'status': [],
            'tipos_inconsistencia': [],
            'cfops': [],
            'ncms': [],
            'csts': []
        }

        # Extrair números de nota fiscal (3 a 10 dígitos)
        numeros_nota = re.findall(r'\b\d{3,10}\b', pergunta)
        entidades['numeros_nota'] = numeros_nota

        # Extrair tipos de tributo (mais abrangente)
        tributos_conhecidos = [
            'icms', 'ipi', 'pis', 'cofins', 'difal', 'icms-st', 'icms_st',
            'imposto', 'tributo', 'taxa'
        ]
        for tributo in tributos_conhecidos:
            if tributo.lower() in pergunta.lower():
                entidades['tributos'].append(tributo.lower().replace('-', '_'))

        # Extrair anos (2020-2030)
        anos = re.findall(r'\b(20[2-3]\d)\b', pergunta)
        entidades['anos'] = [int(ano) for ano in anos]

        # Extrair meses (nomes e números)
        meses_nomes = {
            'janeiro': 1, 'fevereiro': 2, 'março': 3, 'abril': 4, 'maio': 5, 'junho': 6,
            'julho': 7, 'agosto': 8, 'setembro': 9, 'outubro': 10, 'novembro': 11, 'dezembro': 12,
            'jan': 1, 'fev': 2, 'mar': 3, 'abr': 4, 'mai': 5, 'jun': 6,
            'jul': 7, 'ago': 8, 'set': 9, 'out': 10, 'nov': 11, 'dez': 12
        }

        for nome_mes, numero_mes in meses_nomes.items():
            if nome_mes.lower() in pergunta.lower():
                entidades['meses'].append(numero_mes)

        # Extrair números de mês (1-12)
        meses_numeros = re.findall(r'\b(1[0-2]|[1-9])\b', pergunta)
        for mes in meses_numeros:
            if 1 <= int(mes) <= 12:
                entidades['meses'].append(int(mes))

        # Extrair valores monetários
        valores = re.findall(r'R\$\s*(\d{1,3}(?:\.\d{3})*(?:,\d{2})?)', pergunta)
        entidades['valores_monetarios'] = valores

        # Extrair percentuais
        percentuais = re.findall(r'(\d+(?:,\d+)?)\s*%', pergunta)
        entidades['percentuais'] = percentuais

        # Extrair status
        status_conhecidos = ['conforme', 'inconsistente', 'novo', 'produção', 'producao', 'ativo', 'inativo']
        for status in status_conhecidos:
            if status.lower() in pergunta.lower():
                entidades['status'].append(status.lower().replace('ç', 'c'))

        # Extrair tipos de inconsistência
        inconsistencias = ['valor', 'cst', 'origem', 'aliquota', 'alíquota', 'base de calculo', 'base de cálculo']
        for inconsistencia in inconsistencias:
            if inconsistencia.lower() in pergunta.lower():
                entidades['tipos_inconsistencia'].append(inconsistencia.lower().replace('í', 'i').replace('ç', 'c'))

        # Extrair CFOPs (4 dígitos)
        cfops = re.findall(r'\b([1-7]\d{3})\b', pergunta)
        entidades['cfops'] = cfops

        # Extrair NCMs (8 dígitos)
        ncms = re.findall(r'\b(\d{8})\b', pergunta)
        entidades['ncms'] = ncms

        # Extrair CSTs (2-3 dígitos)
        csts = re.findall(r'\bCST\s*(\d{2,3})\b', pergunta, re.IGNORECASE)
        entidades['csts'] = csts

        return entidades

    def _gerar_consulta_sql(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera consulta SQL baseada na pergunta e entidades extraídas
        """
        # Buscar template mais adequado
        template = self._encontrar_template_adequado(pergunta)

        if template:
            return self._aplicar_template(template, entidades, empresa_id)

        # Se não encontrou template, gerar consulta genérica
        return self._gerar_consulta_generica(pergunta, entidades, empresa_id)

    def _encontrar_template_adequado(self, pergunta: str) -> Optional[Dict]:
        """
        Encontra o template mais adequado para a pergunta
        """
        pergunta_lower = pergunta.lower()

        # Templates específicos baseados em palavras-chave e contexto
        templates_internos = {
            # Consultas sobre notas fiscais específicas
            'nota_especifica': {
                'keywords': ['nota', 'nf', 'numero'],
                'sql_template': """
                    SELECT
                        nfi.numero_nf,
                        nfi.chave_nf,
                        nfi.data_emissao,
                        nfi.cfop,
                        nfi.ncm,
                        nfi.valor_total,
                        e.razao_social as empresa_nome,
                        c.razao_social as cliente_nome,
                        p.descricao as produto_nome,
                        p.codigo as produto_codigo,
                        -- Dados dos tributos da nota
                        t.icms_valor, t.icms_aliquota, t.icms_cst, t.icms_vbc,
                        t.ipi_valor, t.ipi_aliquota, t.ipi_cst, t.ipi_vbc,
                        t.pis_valor, t.pis_aliquota, t.pis_cst, t.pis_vbc,
                        t.cofins_valor, t.cofins_aliquota, t.cofins_cst, t.cofins_vbc
                    FROM nota_fiscal_item nfi
                    LEFT JOIN empresa e ON nfi.empresa_id = e.id
                    LEFT JOIN cliente c ON nfi.cliente_id = c.id
                    LEFT JOIN produto p ON nfi.produto_id = p.id
                    LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id
                """,
                'categoria': 'nota_fiscal'
            },

            # Consultas sobre auditorias e inconsistências
            'auditoria_inconsistencias': {
                'keywords': ['inconsistencia', 'inconsistente', 'problema', 'erro', 'auditoria'],
                'sql_template': """
                    SELECT
                        ar.tipo_tributo,
                        ar.status,
                        COUNT(*) as total_registros,
                        SUM(ar.valor_nota) as valor_total_nota,
                        SUM(ar.valor_calculado) as valor_total_calculado,
                        SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
                        AVG(ar.valor_nota - ar.valor_calculado) as diferenca_media,
                        -- Detalhes das inconsistências
                        SUM(CASE WHEN ar.inconsistencia_valor THEN 1 ELSE 0 END) as inconsistencias_valor,
                        SUM(CASE WHEN ar.inconsistencia_cst THEN 1 ELSE 0 END) as inconsistencias_cst,
                        SUM(CASE WHEN ar.inconsistencia_aliquota THEN 1 ELSE 0 END) as inconsistencias_aliquota
                    FROM auditoria_resultado ar
                    LEFT JOIN nota_fiscal_item nfi ON ar.nota_fiscal_item_id = nfi.id
                    LEFT JOIN empresa e ON ar.empresa_id = e.id
                """,
                'categoria': 'auditoria'
            },

            # Consultas sobre produtos
            'produtos_detalhados': {
                'keywords': ['produto', 'produtos', 'mercadoria'],
                'sql_template': """
                    SELECT
                        p.codigo,
                        p.descricao,
                        p.cest,
                        e.razao_social as empresa_nome,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        SUM(nfi.valor_total) as valor_total_vendas,
                        AVG(nfi.valor_total) as valor_medio_venda,
                        -- Dados tributários mais comuns
                        COUNT(DISTINCT nfi.cfop) as cfops_utilizados,
                        COUNT(DISTINCT nfi.ncm) as ncms_utilizados
                    FROM produto p
                    LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
                    LEFT JOIN empresa e ON p.empresa_id = e.id
                """,
                'categoria': 'produto'
            },

            # Consultas sobre clientes
            'clientes_detalhados': {
                'keywords': ['cliente', 'clientes', 'destinatario'],
                'sql_template': """
                    SELECT
                        c.razao_social,
                        c.cnpj,
                        c.uf,
                        c.municipio,
                        c.atividade,
                        c.destinacao,
                        e.razao_social as empresa_nome,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        SUM(nfi.valor_total) as valor_total_compras,
                        AVG(nfi.valor_total) as valor_medio_compra
                    FROM cliente c
                    LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
                    LEFT JOIN empresa e ON c.empresa_id = e.id
                """,
                'categoria': 'cliente'
            },

            # Consultas sobre empresas
            'empresas_detalhadas': {
                'keywords': ['empresa', 'empresas'],
                'sql_template': """
                    SELECT
                        e.razao_social,
                        e.cnpj,
                        e.inscricao_estadual,
                        e.atividade,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        SUM(nfi.valor_total) as valor_total_operacoes,
                        AVG(nfi.valor_total) as valor_medio_operacao
                    FROM empresa e
                    LEFT JOIN nota_fiscal_item nfi ON e.id = nfi.empresa_id
                """,
                'categoria': 'empresa'
            },

            # Consultas sobre NCMs
            'ncm_analise': {
                'keywords': ['ncm', 'ncms', 'classificacao'],
                'sql_template': """
                    SELECT
                        nfi.ncm,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        SUM(nfi.valor_total) as valor_total,
                        AVG(nfi.valor_total) as valor_medio
                    FROM nota_fiscal_item nfi
                    WHERE nfi.ncm IS NOT NULL AND nfi.ncm != ''
                """,
                'categoria': 'ncm'
            },

            # Consultas sobre CFOPs
            'cfop_analise': {
                'keywords': ['cfop', 'cfops', 'operacao'],
                'sql_template': """
                    SELECT
                        nfi.cfop,
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        SUM(nfi.valor_total) as valor_total,
                        AVG(nfi.valor_total) as valor_medio
                    FROM nota_fiscal_item nfi
                    WHERE nfi.cfop IS NOT NULL AND nfi.cfop != ''
                """,
                'categoria': 'cfop'
            },

            # Consultas estatísticas gerais
            'estatisticas_gerais': {
                'keywords': ['total', 'quantidade', 'quantos', 'estatistica', 'resumo'],
                'sql_template': """
                    SELECT
                        COUNT(DISTINCT nfi.numero_nf) as total_notas,
                        COUNT(DISTINCT nfi.empresa_id) as total_empresas,
                        COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                        COUNT(DISTINCT nfi.produto_id) as total_produtos,
                        SUM(nfi.valor_total) as valor_total_operacoes,
                        AVG(nfi.valor_total) as valor_medio_operacao,
                        MIN(nfi.data_emissao) as data_mais_antiga,
                        MAX(nfi.data_emissao) as data_mais_recente
                    FROM nota_fiscal_item nfi
                """,
                'categoria': 'estatistica'
            }
        }

        # Verificar templates internos primeiro
        for template_name, template_data in templates_internos.items():
            keywords = template_data['keywords']
            if any(keyword in pergunta_lower for keyword in keywords):
                return {
                    'categoria': template_data['categoria'],
                    'sql_template': template_data['sql_template'],
                    'pergunta_template': template_name
                }

        # Verificar se é uma consulta específica sobre tributo
        tributo_especifico = self._detectar_tributo_especifico(pergunta_lower)
        if tributo_especifico:
            return {
                'categoria': f'tributo_{tributo_especifico}',
                'sql_template': f"""
                    SELECT
                        ar.status,
                        COUNT(*) as total_registros,
                        SUM(ar.valor_nota) as valor_total_nota,
                        SUM(ar.valor_calculado) as valor_total_calculado,
                        SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
                        AVG(ar.valor_nota) as valor_medio_nota,
                        AVG(ar.valor_calculado) as valor_medio_calculado
                    FROM auditoria_resultado ar
                    WHERE ar.tipo_tributo = '{tributo_especifico}'
                """,
                'pergunta_template': f'tributo_{tributo_especifico}'
            }

        # Buscar templates do banco de dados
        try:
            templates = ChatbotTemplates.query.filter_by(ativo=True).all()

            for template in templates:
                # Verificar se a pergunta contém palavras-chave do template
                palavras_template = template.pergunta_template.lower().split()
                palavras_pergunta = pergunta.lower().split()

                matches = sum(1 for palavra in palavras_template if palavra in palavras_pergunta)
                if matches >= len(palavras_template) * 0.6:  # 60% de match
                    return {
                        'categoria': template.categoria,
                        'sql_template': template.sql_template,
                        'pergunta_template': template.pergunta_template
                    }
        except Exception as e:
            print(f"Erro ao buscar templates: {e}")

        return None

    def _detectar_tributo_especifico(self, pergunta_lower: str) -> Optional[str]:
        """
        Detecta se a pergunta é sobre um tributo específico
        """
        tributos_map = {
            'icms': ['icms', 'imposto sobre circulacao', 'circulacao de mercadorias'],
            'ipi': ['ipi', 'imposto sobre produtos industrializados', 'produtos industrializados'],
            'pis': ['pis', 'programa de integracao social'],
            'cofins': ['cofins', 'contribuicao para financiamento'],
            'difal': ['difal', 'diferencial de aliquota'],
            'icms_st': ['icms-st', 'icms st', 'substituicao tributaria', 'substitucao tributaria']
        }

        for tributo, keywords in tributos_map.items():
            if any(keyword in pergunta_lower for keyword in keywords):
                # Verificar se não é uma consulta geral sobre todos os tributos
                if not any(palavra in pergunta_lower for palavra in ['todos', 'geral', 'resumo', 'total']):
                    return tributo.replace('_', '_')  # Manter formato do banco

        return None

    def _aplicar_template(self, template: Dict, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Aplica um template encontrado, substituindo variáveis e adicionando filtros
        """
        sql = template['sql_template']
        parametros = {}
        where_conditions = []

        # Adicionar filtros baseados nas entidades extraídas

        # Filtro por empresa
        if empresa_id:
            where_conditions.append(f"e.id = {empresa_id}")

        # Filtro por número de nota fiscal
        if entidades['numeros_nota']:
            numero_nota = entidades['numeros_nota'][0]
            where_conditions.append(f"nfi.numero_nf = '{numero_nota}'")

        # Filtro por tipo de tributo (para consultas de auditoria)
        if entidades['tributos'] and 'auditoria_resultado' in sql:
            tributo = entidades['tributos'][0]
            where_conditions.append(f"ar.tipo_tributo = '{tributo}'")

        # Filtro por status
        if entidades['status']:
            status = entidades['status'][0]
            if 'auditoria_resultado' in sql:
                where_conditions.append(f"ar.status = '{status}'")
            elif 'cenario_' in sql:
                where_conditions.append(f"status = '{status}'")

        # Filtro por ano
        if entidades['anos']:
            ano = entidades['anos'][0]
            where_conditions.append(f"EXTRACT(YEAR FROM nfi.data_emissao) = {ano}")

        # Filtro por mês
        if entidades['meses']:
            mes = entidades['meses'][0]
            where_conditions.append(f"EXTRACT(MONTH FROM nfi.data_emissao) = {mes}")

        # Filtro por CFOP
        if entidades['cfops']:
            cfop = entidades['cfops'][0]
            where_conditions.append(f"nfi.cfop = '{cfop}'")

        # Filtro por NCM
        if entidades['ncms']:
            ncm = entidades['ncms'][0]
            where_conditions.append(f"nfi.ncm = '{ncm}'")

        # Filtro por CST
        if entidades['csts']:
            cst = entidades['csts'][0]
            if 'icms' in template.get('categoria', ''):
                where_conditions.append(f"t.icms_cst = '{cst}'")
            elif 'ipi' in template.get('categoria', ''):
                where_conditions.append(f"t.ipi_cst = '{cst}'")

        # Aplicar filtros WHERE
        if where_conditions:
            if 'WHERE' in sql.upper():
                sql += ' AND ' + ' AND '.join(where_conditions)
            else:
                sql += ' WHERE ' + ' AND '.join(where_conditions)

        # Adicionar GROUP BY se necessário para consultas agregadas
        if 'COUNT(' in sql or 'SUM(' in sql or 'AVG(' in sql:
            if 'GROUP BY' not in sql.upper():
                # Determinar campos para GROUP BY baseado no tipo de consulta
                categoria = template.get('categoria', '')
                if categoria == 'auditoria':
                    sql += ' GROUP BY ar.tipo_tributo, ar.status'
                elif categoria == 'produto':
                    sql += ' GROUP BY p.id, p.codigo, p.descricao, p.cest, e.razao_social'
                elif categoria == 'cliente':
                    sql += ' GROUP BY c.id, c.razao_social, c.cnpj, c.uf, c.municipio, c.atividade, c.destinacao, e.razao_social'
                elif categoria == 'empresa':
                    sql += ' GROUP BY e.id, e.razao_social, e.cnpj, e.inscricao_estadual, e.atividade'
                elif categoria == 'ncm':
                    sql += ' GROUP BY nfi.ncm'
                elif categoria == 'cfop':
                    sql += ' GROUP BY nfi.cfop'

        # Adicionar ORDER BY para melhor apresentação
        if 'ORDER BY' not in sql.upper():
            categoria = template.get('categoria', '')
            if categoria == 'nota_fiscal':
                sql += ' ORDER BY nfi.data_emissao DESC'
            elif categoria == 'auditoria':
                sql += ' ORDER BY total_registros DESC'
            elif categoria in ['produto', 'cliente', 'empresa']:
                sql += ' ORDER BY total_notas DESC'
            elif categoria in ['ncm', 'cfop']:
                sql += ' ORDER BY total_notas DESC'
            else:
                sql += ' ORDER BY 1'

        # Limitar resultados para evitar sobrecarga
        if 'LIMIT' not in sql.upper():
            sql += ' LIMIT 50'

        return sql, parametros

    def _gerar_consulta_generica(self, pergunta: str, entidades: Dict, empresa_id: Optional[int]) -> Tuple[str, Dict]:
        """
        Gera uma consulta genérica quando não encontra template específico
        """
        parametros = {}
        where_conditions = []

        # Adicionar filtro de empresa sempre que possível
        if empresa_id:
            where_conditions.append(f"nfi.empresa_id = {empresa_id}")

        # Priorizar consultas baseadas nas entidades encontradas
        if entidades['numeros_nota']:
            # Consulta detalhada para nota fiscal específica
            numero_nota = entidades['numeros_nota'][0]
            sql = """
                SELECT
                    nfi.numero_nf,
                    nfi.chave_nf,
                    nfi.data_emissao,
                    nfi.cfop,
                    nfi.ncm,
                    nfi.valor_total,
                    e.razao_social as empresa_nome,
                    c.razao_social as cliente_nome,
                    c.cnpj as cliente_cnpj,
                    c.uf as cliente_uf,
                    p.descricao as produto_nome,
                    p.codigo as produto_codigo,
                    -- Dados tributários da nota
                    t.icms_valor, t.icms_aliquota, t.icms_cst, t.icms_vbc,
                    t.ipi_valor, t.ipi_aliquota, t.ipi_cst, t.ipi_vbc,
                    t.pis_valor, t.pis_aliquota, t.pis_cst, t.pis_vbc,
                    t.cofins_valor, t.cofins_aliquota, t.cofins_cst, t.cofins_vbc,
                    -- Dados de auditoria se existirem
                    ar.status as auditoria_status,
                    ar.valor_calculado as valor_auditoria_calculado,
                    ar.tipo_tributo as tributo_auditado
                FROM nota_fiscal_item nfi
                LEFT JOIN empresa e ON nfi.empresa_id = e.id
                LEFT JOIN cliente c ON nfi.cliente_id = c.id
                LEFT JOIN produto p ON nfi.produto_id = p.id
                LEFT JOIN tributo t ON t.nota_fiscal_item_id = nfi.id
                LEFT JOIN auditoria_resultado ar ON ar.nota_fiscal_item_id = nfi.id
            """
            where_conditions.append(f"nfi.numero_nf = '{numero_nota}'")

        elif any(keyword in pergunta.lower() for keyword in ['inconsistencia', 'inconsistente', 'problema', 'erro', 'auditoria']):
            # Consulta para inconsistências de auditoria
            sql = """
                SELECT
                    ar.tipo_tributo,
                    ar.status,
                    COUNT(*) as total_registros,
                    SUM(ar.valor_nota) as valor_total_nota,
                    SUM(ar.valor_calculado) as valor_total_calculado,
                    SUM(ar.valor_nota - ar.valor_calculado) as diferenca_total,
                    AVG(ABS(ar.valor_nota - ar.valor_calculado)) as diferenca_media,
                    -- Tipos de inconsistências
                    SUM(CASE WHEN ar.inconsistencia_valor THEN 1 ELSE 0 END) as inconsistencias_valor,
                    SUM(CASE WHEN ar.inconsistencia_cst THEN 1 ELSE 0 END) as inconsistencias_cst,
                    SUM(CASE WHEN ar.inconsistencia_aliquota THEN 1 ELSE 0 END) as inconsistencias_aliquota,
                    SUM(CASE WHEN ar.inconsistencia_base_calculo THEN 1 ELSE 0 END) as inconsistencias_base_calculo
                FROM auditoria_resultado ar
                LEFT JOIN nota_fiscal_item nfi ON ar.nota_fiscal_item_id = nfi.id
                LEFT JOIN empresa e ON ar.empresa_id = e.id
            """
            if not entidades['status'] or 'inconsistente' in entidades['status']:
                where_conditions.append("ar.status = 'inconsistente'")

        elif any(keyword in pergunta.lower() for keyword in ['produto', 'produtos', 'mercadoria']):
            # Consulta detalhada para produtos
            sql = """
                SELECT
                    p.codigo,
                    p.descricao,
                    p.cest,
                    e.razao_social as empresa_nome,
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                    SUM(nfi.valor_total) as valor_total_vendas,
                    AVG(nfi.valor_total) as valor_medio_venda,
                    MIN(nfi.data_emissao) as primeira_venda,
                    MAX(nfi.data_emissao) as ultima_venda,
                    -- Dados tributários
                    COUNT(DISTINCT nfi.cfop) as cfops_utilizados,
                    COUNT(DISTINCT nfi.ncm) as ncms_utilizados
                FROM produto p
                LEFT JOIN nota_fiscal_item nfi ON p.id = nfi.produto_id
                LEFT JOIN empresa e ON p.empresa_id = e.id
            """

        elif any(keyword in pergunta.lower() for keyword in ['cliente', 'clientes', 'destinatario']):
            # Consulta detalhada para clientes
            sql = """
                SELECT
                    c.razao_social,
                    c.cnpj,
                    c.uf,
                    c.municipio,
                    c.atividade,
                    c.destinacao,
                    e.razao_social as empresa_nome,
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.produto_id) as total_produtos,
                    SUM(nfi.valor_total) as valor_total_compras,
                    AVG(nfi.valor_total) as valor_medio_compra,
                    MIN(nfi.data_emissao) as primeira_compra,
                    MAX(nfi.data_emissao) as ultima_compra
                FROM cliente c
                LEFT JOIN nota_fiscal_item nfi ON c.id = nfi.cliente_id
                LEFT JOIN empresa e ON c.empresa_id = e.id
            """

        else:
            # Consulta estatística geral
            sql = """
                SELECT
                    COUNT(DISTINCT nfi.numero_nf) as total_notas,
                    COUNT(DISTINCT nfi.empresa_id) as total_empresas,
                    COUNT(DISTINCT nfi.cliente_id) as total_clientes,
                    COUNT(DISTINCT nfi.produto_id) as total_produtos,
                    SUM(nfi.valor_total) as valor_total_operacoes,
                    AVG(nfi.valor_total) as valor_medio_operacao,
                    MIN(nfi.data_emissao) as data_mais_antiga,
                    MAX(nfi.data_emissao) as data_mais_recente,
                    -- Estatísticas de auditoria
                    (SELECT COUNT(*) FROM auditoria_resultado ar2 WHERE ar2.status = 'conforme'
                     {empresa_filter}) as total_conforme,
                    (SELECT COUNT(*) FROM auditoria_resultado ar2 WHERE ar2.status = 'inconsistente'
                     {empresa_filter}) as total_inconsistente
                FROM nota_fiscal_item nfi
            """.format(
                empresa_filter=f"AND ar2.empresa_id = {empresa_id}" if empresa_id else ""
            )

        # Aplicar filtros adicionais baseados nas entidades
        if entidades['tributos'] and 'auditoria_resultado' in sql:
            tributo = entidades['tributos'][0]
            where_conditions.append(f"ar.tipo_tributo = '{tributo}'")

        if entidades['anos']:
            ano = entidades['anos'][0]
            where_conditions.append(f"EXTRACT(YEAR FROM nfi.data_emissao) = {ano}")

        if entidades['meses']:
            mes = entidades['meses'][0]
            where_conditions.append(f"EXTRACT(MONTH FROM nfi.data_emissao) = {mes}")

        if entidades['cfops']:
            cfop = entidades['cfops'][0]
            where_conditions.append(f"nfi.cfop = '{cfop}'")

        if entidades['ncms']:
            ncm = entidades['ncms'][0]
            where_conditions.append(f"nfi.ncm = '{ncm}'")

        # Aplicar condições WHERE
        if where_conditions:
            sql += ' WHERE ' + ' AND '.join(where_conditions)

        # Adicionar GROUP BY para consultas agregadas
        if 'COUNT(' in sql or 'SUM(' in sql or 'AVG(' in sql:
            if 'GROUP BY' not in sql.upper() and not entidades['numeros_nota']:
                if 'produto' in pergunta.lower():
                    sql += ' GROUP BY p.id, p.codigo, p.descricao, p.cest, e.razao_social'
                elif 'cliente' in pergunta.lower():
                    sql += ' GROUP BY c.id, c.razao_social, c.cnpj, c.uf, c.municipio, c.atividade, c.destinacao, e.razao_social'
                elif 'empresa' in pergunta.lower():
                    sql += ' GROUP BY e.id, e.razao_social, e.cnpj, e.inscricao_estadual, e.atividade'
                elif 'auditoria_resultado' in sql:
                    sql += ' GROUP BY ar.tipo_tributo, ar.status'

        # Adicionar ORDER BY
        if 'ORDER BY' not in sql.upper():
            if entidades['numeros_nota']:
                sql += ' ORDER BY nfi.data_emissao DESC'
            elif 'auditoria_resultado' in sql:
                sql += ' ORDER BY total_registros DESC'
            elif any(keyword in pergunta.lower() for keyword in ['produto', 'cliente', 'empresa']):
                sql += ' ORDER BY total_notas DESC'
            else:
                sql += ' ORDER BY nfi.data_emissao DESC'

        # Limitar resultados
        if 'LIMIT' not in sql.upper():
            sql += ' LIMIT 50'

        return sql, parametros

    def _executar_consulta(self, sql: str, parametros: Dict) -> List[Dict]:
        """
        Executa a consulta SQL e retorna os resultados
        """
        try:
            result = db.session.execute(text(sql), parametros)
            columns = result.keys()
            rows = result.fetchall()

            # Converter para lista de dicionários
            dados = []
            for row in rows:
                row_dict = {}
                for i, column in enumerate(columns):
                    value = row[i]
                    # Converter tipos especiais para JSON serializável
                    if hasattr(value, 'isoformat'):  # datetime
                        value = value.isoformat()
                    elif hasattr(value, '__float__'):  # Decimal
                        value = float(value)
                    row_dict[column] = value
                dados.append(row_dict)

            return dados

        except Exception as e:
            print(f"Erro ao executar consulta SQL: {e}")
            return []

    def _gerar_resposta_ia(self, pergunta: str, dados: List[Dict], entidades: Dict) -> str:
        """
        Usa a IA para gerar uma resposta natural baseada nos dados
        """
        if not dados:
            return "Não encontrei dados relacionados à sua pergunta. Pode verificar se os parâmetros estão corretos ou se há dados disponíveis para o período/empresa especificados?"

        # Preparar contexto para a IA
        contexto = self._preparar_contexto_ia(dados, entidades)

        # Determinar o tipo de resposta baseado na pergunta e dados
        tipo_resposta = self._determinar_tipo_resposta(pergunta, dados, entidades)

        prompt = f"""
        Você é um assistente especializado em auditoria fiscal brasileira. Você tem conhecimento profundo sobre:
        - Tributos: ICMS, ICMS-ST, IPI, PIS, COFINS, DIFAL
        - Códigos fiscais: CST, CFOP, NCM
        - Processos de auditoria e conformidade fiscal
        - Análise de inconsistências tributárias

        Pergunta do usuário: {pergunta}

        Dados encontrados:
        {contexto}

        Tipo de análise: {tipo_resposta}

        Instruções específicas:
        1. Responda de forma técnica mas acessível
        2. Use os valores exatos dos dados fornecidos
        3. Para inconsistências, explique o que pode estar causando o problema
        4. Para consultas sobre notas fiscais, forneça um resumo completo
        5. Para estatísticas, destaque os pontos mais relevantes
        6. Se houver dados de auditoria, compare valores da nota vs. calculados
        7. Use formatação clara com quebras de linha e listas
        8. Mencione datas, valores monetários e percentuais quando relevantes
        9. Se identificar problemas fiscais, sugira possíveis causas
        10. Mantenha o foco na pergunta específica do usuário

        Contexto adicional sobre os dados:
        - Valores em "valor_nota" são os tributos declarados na nota fiscal
        - Valores em "valor_calculado" são os tributos calculados pelo sistema baseado nos cenários
        - Status "conforme" indica que os valores batem
        - Status "inconsistente" indica divergências que precisam ser analisadas
        - CST define o regime tributário aplicável
        - CFOP indica a natureza da operação
        - NCM classifica a mercadoria para fins tributários
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"Encontrei os dados, mas houve um erro ao gerar a resposta: {str(e)}"

    def _determinar_tipo_resposta(self, pergunta: str, dados: List[Dict], entidades: Dict) -> str:
        """
        Determina o tipo de resposta baseado na pergunta e dados
        """
        pergunta_lower = pergunta.lower()

        if entidades['numeros_nota']:
            return "Análise detalhada de nota fiscal específica"
        elif any(keyword in pergunta_lower for keyword in ['inconsistencia', 'inconsistente', 'problema', 'erro']):
            return "Análise de inconsistências fiscais"
        elif any(keyword in pergunta_lower for keyword in ['produto', 'produtos']):
            return "Análise de produtos e operações"
        elif any(keyword in pergunta_lower for keyword in ['cliente', 'clientes']):
            return "Análise de clientes e destinatários"
        elif any(keyword in pergunta_lower for keyword in ['auditoria', 'conformidade']):
            return "Relatório de auditoria fiscal"
        elif any(keyword in pergunta_lower for keyword in ['total', 'quantidade', 'estatistica']):
            return "Relatório estatístico geral"
        else:
            return "Consulta geral sobre dados fiscais"

    def _preparar_contexto_ia(self, dados: List[Dict], entidades: Dict) -> str:
        """
        Prepara o contexto dos dados para enviar à IA de forma estruturada e inteligente
        """
        if not dados:
            return "Nenhum dado encontrado."

        # Limitar quantidade de dados para não exceder limite de tokens
        dados_limitados = dados[:15]  # Máximo 15 registros

        contexto_parts = []

        # Detectar tipo de dados e formatar adequadamente
        primeiro_registro = dados_limitados[0]

        # Formatação para nota fiscal específica
        if 'numero_nf' in primeiro_registro and len(dados_limitados) == 1:
            nota = primeiro_registro
            contexto_parts.append("=== DETALHES DA NOTA FISCAL ===")
            contexto_parts.append(f"Número: {nota.get('numero_nf', 'N/A')}")
            contexto_parts.append(f"Data de Emissão: {nota.get('data_emissao', 'N/A')}")
            contexto_parts.append(f"CFOP: {nota.get('cfop', 'N/A')}")
            contexto_parts.append(f"NCM: {nota.get('ncm', 'N/A')}")
            contexto_parts.append(f"Valor Total: R$ {nota.get('valor_total', 0):,.2f}")

            contexto_parts.append("\n--- DADOS DA EMPRESA ---")
            contexto_parts.append(f"Empresa: {nota.get('empresa_nome', 'N/A')}")

            contexto_parts.append("\n--- DADOS DO CLIENTE ---")
            contexto_parts.append(f"Cliente: {nota.get('cliente_nome', 'N/A')}")
            contexto_parts.append(f"CNPJ: {nota.get('cliente_cnpj', 'N/A')}")
            contexto_parts.append(f"UF: {nota.get('cliente_uf', 'N/A')}")

            contexto_parts.append("\n--- DADOS DO PRODUTO ---")
            contexto_parts.append(f"Produto: {nota.get('produto_nome', 'N/A')}")
            contexto_parts.append(f"Código: {nota.get('produto_codigo', 'N/A')}")

            # Dados tributários
            contexto_parts.append("\n--- TRIBUTOS DA NOTA ---")
            if nota.get('icms_valor'):
                contexto_parts.append(f"ICMS: R$ {nota.get('icms_valor', 0):,.2f} (Alíquota: {nota.get('icms_aliquota', 0)}%, CST: {nota.get('icms_cst', 'N/A')})")
            if nota.get('ipi_valor'):
                contexto_parts.append(f"IPI: R$ {nota.get('ipi_valor', 0):,.2f} (Alíquota: {nota.get('ipi_aliquota', 0)}%, CST: {nota.get('ipi_cst', 'N/A')})")
            if nota.get('pis_valor'):
                contexto_parts.append(f"PIS: R$ {nota.get('pis_valor', 0):,.2f} (Alíquota: {nota.get('pis_aliquota', 0)}%, CST: {nota.get('pis_cst', 'N/A')})")
            if nota.get('cofins_valor'):
                contexto_parts.append(f"COFINS: R$ {nota.get('cofins_valor', 0):,.2f} (Alíquota: {nota.get('cofins_aliquota', 0)}%, CST: {nota.get('cofins_cst', 'N/A')})")

            # Dados de auditoria se existirem
            if nota.get('auditoria_status'):
                contexto_parts.append("\n--- RESULTADO DA AUDITORIA ---")
                contexto_parts.append(f"Status: {nota.get('auditoria_status', 'N/A')}")
                contexto_parts.append(f"Tributo Auditado: {nota.get('tributo_auditado', 'N/A')}")
                if nota.get('valor_auditoria_calculado'):
                    contexto_parts.append(f"Valor Calculado: R$ {nota.get('valor_auditoria_calculado', 0):,.2f}")

        # Formatação para dados de auditoria/inconsistências
        elif 'tipo_tributo' in primeiro_registro and 'status' in primeiro_registro:
            contexto_parts.append("=== RESULTADOS DE AUDITORIA ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Tributo: {item.get('tipo_tributo', 'N/A').upper()}")
                contexto_parts.append(f"   Status: {item.get('status', 'N/A')}")
                contexto_parts.append(f"   Total de Registros: {item.get('total_registros', 0):,}")

                if item.get('valor_total_nota') is not None:
                    contexto_parts.append(f"   Valor Total na Nota: R$ {item.get('valor_total_nota', 0):,.2f}")
                if item.get('valor_total_calculado') is not None:
                    contexto_parts.append(f"   Valor Total Calculado: R$ {item.get('valor_total_calculado', 0):,.2f}")
                if item.get('diferenca_total') is not None:
                    diferenca = item.get('diferenca_total', 0)
                    contexto_parts.append(f"   Diferença Total: R$ {diferenca:,.2f}")

                # Tipos de inconsistências
                inconsistencias = []
                if item.get('inconsistencias_valor', 0) > 0:
                    inconsistencias.append(f"Valor ({item.get('inconsistencias_valor', 0)})")
                if item.get('inconsistencias_cst', 0) > 0:
                    inconsistencias.append(f"CST ({item.get('inconsistencias_cst', 0)})")
                if item.get('inconsistencias_aliquota', 0) > 0:
                    inconsistencias.append(f"Alíquota ({item.get('inconsistencias_aliquota', 0)})")
                if item.get('inconsistencias_base_calculo', 0) > 0:
                    inconsistencias.append(f"Base de Cálculo ({item.get('inconsistencias_base_calculo', 0)})")

                if inconsistencias:
                    contexto_parts.append(f"   Tipos de Inconsistências: {', '.join(inconsistencias)}")

        # Formatação para dados de produtos
        elif 'codigo' in primeiro_registro and 'descricao' in primeiro_registro:
            contexto_parts.append("=== DADOS DE PRODUTOS ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Produto: {item.get('descricao', 'N/A')}")
                contexto_parts.append(f"   Código: {item.get('codigo', 'N/A')}")
                if item.get('cest'):
                    contexto_parts.append(f"   CEST: {item.get('cest')}")
                if item.get('total_notas'):
                    contexto_parts.append(f"   Total de Notas: {item.get('total_notas', 0):,}")
                if item.get('valor_total_vendas'):
                    contexto_parts.append(f"   Valor Total de Vendas: R$ {item.get('valor_total_vendas', 0):,.2f}")
                if item.get('valor_medio_venda'):
                    contexto_parts.append(f"   Valor Médio por Venda: R$ {item.get('valor_medio_venda', 0):,.2f}")

        # Formatação para dados de clientes
        elif 'razao_social' in primeiro_registro and 'cnpj' in primeiro_registro:
            contexto_parts.append("=== DADOS DE CLIENTES ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\n{i}. Cliente: {item.get('razao_social', 'N/A')}")
                contexto_parts.append(f"   CNPJ: {item.get('cnpj', 'N/A')}")
                contexto_parts.append(f"   UF: {item.get('uf', 'N/A')}")
                if item.get('municipio'):
                    contexto_parts.append(f"   Município: {item.get('municipio')}")
                if item.get('atividade'):
                    contexto_parts.append(f"   Atividade: {item.get('atividade')}")
                if item.get('total_notas'):
                    contexto_parts.append(f"   Total de Notas: {item.get('total_notas', 0):,}")
                if item.get('valor_total_compras'):
                    contexto_parts.append(f"   Valor Total de Compras: R$ {item.get('valor_total_compras', 0):,.2f}")

        # Formatação para estatísticas gerais
        elif 'total_notas' in primeiro_registro and len(dados_limitados) == 1:
            stats = primeiro_registro
            contexto_parts.append("=== ESTATÍSTICAS GERAIS ===")
            contexto_parts.append(f"Total de Notas Fiscais: {stats.get('total_notas', 0):,}")
            contexto_parts.append(f"Total de Empresas: {stats.get('total_empresas', 0):,}")
            contexto_parts.append(f"Total de Clientes: {stats.get('total_clientes', 0):,}")
            contexto_parts.append(f"Total de Produtos: {stats.get('total_produtos', 0):,}")

            if stats.get('valor_total_operacoes'):
                contexto_parts.append(f"Valor Total das Operações: R$ {stats.get('valor_total_operacoes', 0):,.2f}")
            if stats.get('valor_medio_operacao'):
                contexto_parts.append(f"Valor Médio por Operação: R$ {stats.get('valor_medio_operacao', 0):,.2f}")

            contexto_parts.append(f"Período: {stats.get('data_mais_antiga', 'N/A')} a {stats.get('data_mais_recente', 'N/A')}")

            # Estatísticas de auditoria
            if stats.get('total_conforme') is not None:
                contexto_parts.append(f"\n--- AUDITORIA ---")
                contexto_parts.append(f"Registros Conformes: {stats.get('total_conforme', 0):,}")
                contexto_parts.append(f"Registros Inconsistentes: {stats.get('total_inconsistente', 0):,}")

                total_auditoria = stats.get('total_conforme', 0) + stats.get('total_inconsistente', 0)
                if total_auditoria > 0:
                    percentual_conforme = (stats.get('total_conforme', 0) / total_auditoria) * 100
                    contexto_parts.append(f"Percentual de Conformidade: {percentual_conforme:.1f}%")

        # Formatação genérica para outros tipos de dados
        else:
            contexto_parts.append("=== DADOS ENCONTRADOS ===")
            for i, item in enumerate(dados_limitados, 1):
                contexto_parts.append(f"\nRegistro {i}:")
                for key, value in item.items():
                    if value is not None:
                        # Formatação especial para valores monetários
                        if 'valor' in key.lower() and isinstance(value, (int, float)):
                            contexto_parts.append(f"  {key}: R$ {value:,.2f}")
                        # Formatação especial para datas
                        elif 'data' in key.lower():
                            contexto_parts.append(f"  {key}: {value}")
                        else:
                            contexto_parts.append(f"  {key}: {value}")

        # Adicionar informação sobre registros omitidos
        if len(dados) > 15:
            contexto_parts.append(f"\n... e mais {len(dados) - 15} registros similares não exibidos.")

        # Adicionar resumo estatístico se houver muitos registros
        if len(dados) > 5:
            contexto_parts.append(f"\nTotal de registros encontrados: {len(dados)}")

        return "\n".join(contexto_parts)

    def _iniciar_registro_empresa(self, usuario_id: int) -> Dict[str, Any]:
        """
        Inicia o fluxo de registro de uma nova empresa
        """
        self.registro_empresa_em_andamento = True
        self.dados_empresa = {}
        
        return {
            'resposta': (
                'Vou te ajudar a cadastrar uma nova empresa. Vou precisar de algumas informações.\n\n'
                'Por favor, me informe o CNPJ da empresa (apenas números):'
            ),
            'dados': {
                'proximo_campo': 'cnpj',
                'campos_obrigatorios': self.campos_obrigatorios,
                'campos_opcionais': self.campos_opcionais
            }
        }
    
    def _processar_campo_empresa(self, valor: str, campo: str, usuario_id: int) -> Dict[str, Any]:
        """
        Processa o valor de um campo da empresa
        """
        from models import Empresa, Usuario
        from sqlalchemy import or_
        
        valor = valor.strip()
        
        # Validações específicas por campo
        if campo == 'cnpj':
            # Remover caracteres não numéricos
            cnpj = ''.join(filter(str.isdigit, valor))
            if len(cnpj) != 14:
                return {
                    'resposta': 'CNPJ inválido. O CNPJ deve conter 14 dígitos. Por favor, tente novamente:',
                    'proximo_campo': 'cnpj',
                    'dados': {
                        'proximo_campo': 'cnpj',
                        'dados_empresa': self.dados_empresa
                    }
                }
            
            # Verificar se já existe empresa com este CNPJ
            if Empresa.query.filter_by(cnpj=cnpj).first():
                return {
                    'resposta': 'Já existe uma empresa cadastrada com este CNPJ. Por favor, verifique o número e tente novamente:',
                    'proximo_campo': 'cnpj',
                    'dados': {
                        'proximo_campo': 'cnpj',
                        'dados_empresa': self.dados_empresa
                    }
                }
            
            self.dados_empresa['cnpj'] = cnpj
            return {
                'resposta': 'Ótimo! Agora me informe a Razão Social da empresa:',
                'proximo_campo': 'razao_social',
                'dados': {
                    'proximo_campo': 'razao_social',
                    'dados_empresa': self.dados_empresa
                }
            }
        
        elif campo == 'razao_social':
            if not valor:
                return {
                    'resposta': 'A Razão Social é obrigatória. Por favor, informe a Razão Social da empresa:',
                    'proximo_campo': 'razao_social',
                    'dados': {
                        'proximo_campo': 'razao_social',
                        'dados_empresa': self.dados_empresa
                    }
                }
            
            self.dados_empresa['razao_social'] = valor
            
            # Verificar se todos os campos obrigatórios foram preenchidos
            if all(campo in self.dados_empresa for campo in self.campos_obrigatorios):
                return self._solicitar_campo_opcional()
        
        # Processar outros campos
        else:
            self.dados_empresa[campo] = valor if valor.lower() != 'pular' else None
            return self._solicitar_campo_opcional()
            
    def _solicitar_campo_opcional(self) -> Dict[str, Any]:
        """
        Verifica se há campos opcionais a serem preenchidos
        """
        try:
            # Encontrar o próximo campo opcional não preenchido
            proximo_campo = next((campo for campo in self.campos_opcionais 
                                if campo not in self.dados_empresa), None)
            
            if proximo_campo:
                return {
                    'resposta': f'Informe o {self._formatar_nome_campo(proximo_campo)} (ou digite "pular" para pular):',
                    'proximo_campo': proximo_campo,
                    'dados': {
                        'proximo_campo': proximo_campo,
                        'dados_empresa': self.dados_empresa
                    }
                }
            else:
                return self._finalizar_registro_empresa()
        except Exception as e:
            logger.error(f"Erro ao solicitar campo opcional: {str(e)}", exc_info=True)
            return self._erro_registro(f"Ocorreu um erro ao processar os dados. Por favor, tente novamente.")
    
    def _solicitar_campo_opcional(self) -> Dict[str, Any]:
        """
        Verifica se há campos opcionais a serem preenchidos
        """
        # Encontrar o próximo campo opcional não preenchido
        proximo_campo = next((campo for campo in self.campos_opcionais 
                            if campo not in self.dados_empresa), None)
        
        if proximo_campo:
            return {
                'resposta': f'Informe o {self._formatar_nome_campo(proximo_campo)} (ou digite "pular" para pular):',
                'proximo_campo': proximo_campo
            }
        else:
            return self._finalizar_registro_empresa()
    
    def _formatar_nome_campo(self, campo: str) -> str:
        """
        Formata o nome do campo para exibição
        """
        nomes = {
            'nome_fantasia': 'Nome Fantasia',
            'inscricao_estadual': 'Inscrição Estadual',
            'email': 'E-mail',
            'responsavel': 'Nome do Responsável',
            'cep': 'CEP',
            'logradouro': 'Logradouro',
            'numero': 'Número',
            'complemento': 'Complemento',
            'bairro': 'Bairro',
            'cidade': 'Cidade',
            'estado': 'Estado (UF)',
            'cnae': 'CNAE',
            'tributacao': 'Regime Tributário',
            'atividade': 'Atividade Principal',
            'pis_cofins': 'Regime PIS/COFINS',
            'observacoes': 'Observações'
        }
        return nomes.get(campo, campo.replace('_', ' ').title())
    
    def _finalizar_registro_empresa(self) -> Dict[str, Any]:
        """
        Finaliza o registro da empresa
        """
        from models import Empresa, Usuario, db
        
        try:
            # Obter o usuário para verificar permissões
            current_user_id = self.dados_empresa.get('usuario_id')
            if not current_user_id:
                return self._erro_registro('Usuário não autenticado. Por favor, faça login novamente.')
                
            usuario = Usuario.query.get(current_user_id)
            if not usuario:
                return self._erro_registro('Usuário não encontrado')
            
            # Verificar se o usuário tem permissão para cadastrar empresas
            if usuario.tipo_usuario not in ['admin', 'escritorio']:
                return self._erro_registro('Você não tem permissão para cadastrar empresas. Apenas administradores e usuários do tipo escritório podem realizar esta ação.')
            
            # Determinar o escritório_id com base no tipo de usuário
            if usuario.tipo_usuario == 'escritorio':
                escritorio_id = usuario.escritorio_id
            elif 'escritorio_id' in self.dados_empresa:
                escritorio_id = self.dados_empresa['escritorio_id']
            else:
                return self._erro_registro('Não foi possível determinar o escritório para vincular a empresa')
            
            # Verificar se já existe empresa com este CNPJ
            if Empresa.query.filter_by(cnpj=self.dados_empresa['cnpj']).first():
                return self._erro_registro('Já existe uma empresa cadastrada com este CNPJ.')
            
            # Criar a empresa
            try:
                empresa = Empresa(
                    razao_social=self.dados_empresa['razao_social'],
                    cnpj=self.dados_empresa['cnpj'],
                    escritorio_id=escritorio_id,
                    **{k: v for k, v in self.dados_empresa.items() 
                       if k in self.campos_opcionais and v is not None and v != 'pular'}
                )
                
                db.session.add(empresa)
                db.session.flush()  # Gera o ID da empresa sem fazer commit
                
                # Se for um usuário comum, atribuir a empresa a ele
                if usuario.tipo_usuario == 'usuario':
                    empresas_permitidas = usuario.empresas_permitidas or []
                    if empresa.id not in empresas_permitidas:
                        empresas_permitidas.append(empresa.id)
                        usuario.empresas_permitidas = empresas_permitidas
                
                db.session.commit()
                
                # Limpar estado
                self.registro_empresa_em_andamento = False
                dados_empresa = self.dados_empresa.copy()
                self.dados_empresa = {}
                
                return {
                    'resposta': f"✅ Empresa {dados_empresa['razao_social']} cadastrada com sucesso!",
                    'dados': {
                        'registro_concluido': True,
                        'dados_empresa': dados_empresa,
                        'empresa_id': empresa.id
                    }
                }
                
            except Exception as e:
                db.session.rollback()
                logger.error(f"Erro ao salvar empresa no banco de dados: {str(e)}", exc_info=True)
                return self._erro_registro(f"Não foi possível salvar os dados da empresa. Erro: {str(e)}")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Erro ao finalizar registro de empresa: {str(e)}", exc_info=True)
            return self._erro_registro(f"Ocorreu um erro inesperado ao processar o cadastro. Por favor, tente novamente.")
    
    def _erro_registro(self, mensagem: str) -> Dict[str, Any]:
        """
        Retorna uma mensagem de erro e limpa o estado
        """
        self.registro_empresa_em_andamento = False
        self.dados_empresa = {}
        return {
            'resposta': f'❌ {mensagem}',
            'erro': True,
            'dados': {
                'erro': True,
                'mensagem': mensagem
            }
        }
    
    def _validar_cnpj(self, cnpj: str) -> tuple[bool, str]:
        """
        Valida um CNPJ
        
        Retorna:
            tuple: (valido: bool, mensagem: str)
        """
        if not cnpj:
            return False, 'CNPJ não pode estar vazio'
            
        # Remover caracteres não numéricos
        cnpj = ''.join(filter(str.isdigit, str(cnpj)))
        
        # Verificar se tem 14 dígitos
        if len(cnpj) != 14:
            return False, 'CNPJ deve conter 14 dígitos'
            
        # Verificar se todos os dígitos são iguais (ex: 00.000.000/0000-00)
        if len(set(cnpj)) == 1:
            return False, 'CNPJ inválido (todos os dígitos iguais)'
            
        # Verificar dígitos verificadores
        # Cálculo do primeiro dígito verificador
        soma = 0
        peso = 5
        for i in range(12):
            soma += int(cnpj[i]) * peso
            peso = 9 if peso == 2 else peso - 1
            
        resto = soma % 11
        digito1 = 0 if resto < 2 else 11 - resto
        
        # Cálculo do segundo dígito verificador
        soma = 0
        peso = 6
        for i in range(13):
            soma += int(cnpj[i]) * peso
            peso = 9 if peso == 2 else peso - 1
            
        resto = soma % 11
        digito2 = 0 if resto < 2 else 11 - resto
        
        # Verificar se os dígitos calculados conferem com os dígitos informados
        if int(cnpj[12]) == digito1 and int(cnpj[13]) == digito2:
            return True, 'CNPJ válido'
        else:
            return False, 'CNPJ inválido (dígitos verificadores incorretos)'
    
    def _validar_cpf(self, cpf: str) -> tuple[bool, str]:
        """
        Valida um CPF
        
        Retorna:
            tuple: (valido: bool, mensagem: str)
        """
        if not cpf:
            return False, 'CPF não pode estar vazio'
            
        # Remover caracteres não numéricos
        cpf = ''.join(filter(str.isdigit, str(cpf)))
        
        # Verificar se tem 11 dígitos
        if len(cpf) != 11:
            return False, 'CPF deve conter 11 dígitos'
            
        # Verificar se todos os dígitos são iguais (ex: 000.000.000-00)
        if len(set(cpf)) == 1:
            return False, 'CPF inválido (todos os dígitos iguais)'
            
        # Verificar dígitos verificadores
        # Cálculo do primeiro dígito verificador
        soma = 0
        for i in range(9):
            soma += int(cpf[i]) * (10 - i)
            
        resto = 11 - (soma % 11)
        digito1 = resto if resto <= 9 else 0
        
        # Cálculo do segundo dígito verificador
        soma = 0
        for i in range(10):
            soma += int(cpf[i]) * (11 - i)
            
        resto = 11 - (soma % 11)
        digito2 = resto if resto <= 9 else 0
        
        # Verificar se os dígitos calculados conferem com os dígitos informados
        if int(cpf[9]) == digito1 and int(cpf[10]) == digito2:
            return True, 'CPF válido'
        else:
            return False, 'CPF inválido (dígitos verificadores incorretos)'
    
    def _formatar_cpf(self, cpf: str) -> str:
        """
        Formata o CPF para exibição
        """
        if not cpf:
            return ''
            
        # Remover caracteres não numéricos
        cpf = ''.join(filter(str.isdigit, str(cpf)))
        
        if len(cpf) != 11:
            return cpf  # Retorna sem formatação se não tiver 11 dígitos
            
        return f'{cpf[:3]}.{cpf[3:6]}.{cpf[6:9]}-{cpf[9:]}'
    
    def _validar_email(self, email: str) -> tuple[bool, str]:
        """
        Valida um endereço de e-mail
        
        Retorna:
            tuple: (valido: bool, mensagem: str)
        """
        if not email or not email.strip():
            return False, 'E-mail não pode estar vazio'
            
        email = email.strip()
        
        # Expressão regular para validar e-mail
        import re
        padrao = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(padrao, email):
            return False, 'E-mail inválido. Por favor, insira um e-mail válido.'
            
        # Verificar se o domínio tem pelo menos um ponto
        if '.' not in email.split('@')[1]:
            return False, 'Domínio de e-mail inválido. Verifique o endereço informado.'
            
        return True, 'E-mail válido'
    
    def _validar_telefone(self, telefone: str) -> tuple[bool, str]:
        """
        Valida um número de telefone no formato brasileiro
        
        Aceita os formatos:
        - (XX) XXXX-XXXX
        - (XX) XXXXX-XXXX
        - XX XXXX-XXXX
        - XX XXXXX-XXXX
        - XXXXXXXXXX
        - XXXXXXXXXXX
        
        Retorna:
            tuple: (valido: bool, mensagem: str, telefone_formatado: str)
        """
        if not telefone or not telefone.strip():
            return False, 'Telefone não pode estar vazio', ''
            
        # Remover caracteres não numéricos
        telefone_limpo = ''.join(filter(str.isdigit, str(telefone)))
        
        # Verificar se tem 10 (telefone fixo) ou 11 (celular) dígitos
        if len(telefone_limpo) not in [10, 11]:
            return False, 'Telefone deve ter 10 ou 11 dígitos (DD + número)', ''
            
        # Formatar o telefone
        ddd = telefone_limpo[:2]
        numero = telefone_limpo[2:]
        
        if len(numero) == 8:  # Telefone fixo
            telefone_formatado = f'({ddd}) {numero[:4]}-{numero[4:]}'
        else:  # Celular
            telefone_formatado = f'({ddd}) {numero[:5]}-{numero[5:]}'
            
        return True, 'Telefone válido', telefone_formatado
    
    def _validar_cep(self, cep: str) -> tuple[bool, str, str]:
        """
        Valida um CEP no formato brasileiro
        
        Aceita os formatos:
        - XXXXX-XXX
        - XXXXXXXX
        
        Retorna:
            tuple: (valido: bool, mensagem: str, cep_formatado: str)
        """
        if not cep or not cep.strip():
            return False, 'CEP não pode estar vazio', ''
            
        # Remover caracteres não numéricos
        cep_limpo = ''.join(filter(str.isdigit, str(cep)))
        
        # Verificar se tem 8 dígitos
        if len(cep_limpo) != 8:
            return False, 'CEP deve conter 8 dígitos', ''
            
        # Formatar CEP
        cep_formatado = f'{cep_limpo[:5]}-{cep_limpo[5:]}'
        
        return True, 'CEP válido', cep_formatado
    
    def _validar_data_br(self, data: str) -> tuple[bool, str, str]:
        """
        Valida uma data no formato brasileiro (DD/MM/AAAA)
        
        Aceita os formatos:
        - DD/MM/AAAA
        - DD-MM-AAAA
        - DDMMAAAA
        
        Retorna:
            tuple: (valido: bool, mensagem: str, data_formatada: str)
        """
        if not data or not data.strip():
            return False, 'Data não pode estar vazia', ''
            
        import re
        from datetime import datetime
        
        # Remover caracteres não numéricos
        data_limpa = re.sub(r'[^0-9]', '', data)
        
        # Verificar se tem 8 dígitos
        if len(data_limpa) != 8:
            return False, 'Data deve conter 8 dígitos (DDMMAAAA)', ''
            
        try:
            # Extrair dia, mês e ano
            dia = int(data_limpa[:2])
            mes = int(data_limpa[2:4])
            ano = int(data_limpa[4:])
            
            # Verificar se a data é válida
            if mes < 1 or mes > 12:
                return False, 'Mês inválido. Deve estar entre 01 e 12', ''
                
            if dia < 1 or dia > 31:
                return False, 'Dia inválido', ''
                
            # Verificar meses com 30 dias
            if mes in [4, 6, 9, 11] and dia > 30:
                return False, f'O mês {mes:02d} só tem 30 dias', ''
                
            # Verificar fevereiro e anos bissextos
            if mes == 2:
                # Verificar ano bissexto
                if (ano % 400 == 0) or (ano % 100 != 0 and ano % 4 == 0):
                    if dia > 29:
                        return False, 'Fevereiro tem no máximo 29 dias em anos bissextos', ''
                elif dia > 28:
                    return False, 'Fevereiro tem no máximo 28 dias', ''
            
            # Formatar data
            data_obj = datetime(ano, mes, dia)
            data_formatada = data_obj.strftime('%d/%m/%Y')
            
            return True, 'Data válida', data_formatada
            
        except ValueError as e:
            return False, f'Data inválida: {str(e)}', ''
    
    def _validar_url(self, url: str) -> tuple[bool, str]:
        """
        Valida uma URL
        
        Retorna:
            tuple: (valido: bool, mensagem: str)
        """
        if not url or not url.strip():
            return False, 'URL não pode estar vazia'
            
        import re
        
        # Expressão regular para validar URL
        padrao = re.compile(
            r'^(https?:\/\/)?'  # http:// ou https://
            r'(([A-Z0-9]([A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # domínio
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # ou IP
            r'(?::\d+)?'  # porta
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        if not re.match(padrao, url):
            return False, 'URL inválida. Exemplo de formato válido: https://www.exemplo.com'
            
        return True, 'URL válida'
    
    def _validar_senha_forte(self, senha: str, min_caracteres: int = 8) -> tuple[bool, list[str]]:
        """
        Valida se uma senha é forte
        
        Args:
            senha: A senha a ser validada
            min_caracteres: Número mínimo de caracteres (padrão: 8)
            
        Retorna:
            tuple: (valido: bool, erros: list[str])
        """
        if not senha:
            return False, ['A senha não pode estar vazia']
            
        erros = []
        
        # Verificar comprimento mínimo
        if len(senha) < min_caracteres:
            erros.append(f'A senha deve ter pelo menos {min_caracteres} caracteres')
            
        # Verificar se tem letra minúscula
        if not any(c.islower() for c in senha):
            erros.append('A senha deve conter pelo menos uma letra minúscula')
            
        # Verificar se tem letra maiúscula
        if not any(c.isupper() for c in senha):
            erros.append('A senha deve conter pelo menos uma letra maiúscula')
            
        # Verificar se tem número
        if not any(c.isdigit() for c in senha):
            erros.append('A senha deve conter pelo menos um número')
            
        # Verificar se tem caractere especial
        caracteres_especiais = '!@#$%^&*()_+-=[]{}|;:,.<>?/`~"\''
        if not any(c in caracteres_especiais for c in senha):
            erros.append('A senha deve conter pelo menos um caractere especial')
            
        # Verificar se tem espaços
        if ' ' in senha:
            erros.append('A senha não pode conter espaços em branco')
            
        return len(erros) == 0, erros
    
    def _validar_e_formatar_moeda_br(self, valor: str) -> tuple[bool, str, float]:
        """
        Valida e formata um valor monetário no formato brasileiro
        
        Aceita os formatos:
        - 1.234,56
        - 1,234.56
        - 1234.56
        - 1234,56
        - R$ 1.234,56
        
        Retorna:
            tuple: (valido: bool, mensagem: str, valor_float: float)
        """
        if not valor or not valor.strip():
            return False, 'Valor não pode estar vazio', 0.0
            
        import re
        
        # Remover caracteres não numéricos, exceto vírgula e ponto
        valor_limpo = re.sub(r'[^0-9,.]', '', str(valor).strip())
        
        # Contar vírgulas e pontos para determinar o formato
        tem_virgula = ',' in valor_limpo
        tem_ponto = '.' in valor_limpo
        
        try:
            if tem_virgula and tem_ponto:
                # Formato 1.234,56 ou 1,234.56
                if valor_limpo.rfind(',') > valor_limpo.rfind('.'):
                    # Último separador é vírgula (1.234,56)
                    valor_float = float(valor_limpo.replace('.', '').replace(',', '.'))
                else:
                    # Último separador é ponto (1,234.56)
                    valor_float = float(valor_limpo.replace(',', ''))
            elif tem_virgula:
                # Formato 1234,56
                valor_float = float(valor_limpo.replace(',', '.'))
            elif tem_ponto:
                # Formato 1234.56
                valor_float = float(valor_limpo)
            else:
                # Apenas números
                valor_float = float(valor_limpo)
                
            # Verificar se o valor é positivo
            if valor_float < 0:
                return False, 'O valor não pode ser negativo', 0.0
                
            # Formatar para o padrão brasileiro (1.234,56)
            valor_formatado = f'R$ {valor_float:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.')
            
            return True, 'Valor válido', valor_float
            
        except (ValueError, TypeError):
            return False, 'Valor monetário inválido. Exemplos de formatos aceitos: 1.234,56 ou 1234.56', 0.0
    
    def _validar_numero_documento_fiscal(self, numero: str, tipo: str = 'NFe') -> tuple[bool, str, str]:
        """
        Valida e formata números de documentos fiscais brasileiros
        
        Args:
            numero: Número do documento fiscal (apenas dígitos)
            tipo: Tipo do documento fiscal (NFe, NFCe, CTe, MDFe, etc.)
            
        Retorna:
            tuple: (valido: bool, mensagem: str, numero_formatado: str)
        """
        if not numero or not str(numero).strip():
            return False, f'Número do {tipo} não pode estar vazio', ''
            
        # Remover caracteres não numéricos
        numero_limpo = ''.join(filter(str.isdigit, str(numero)))
        
        # Mapear tipos de documento para seus respectivos comprimentos
        comprimentos = {
            'NFe': 44,    # Chave de acesso da NFe
            'NFCe': 44,   # Chave de acesso da NFCe
            'CTe': 44,    # Chave de acesso do CTe
            'MDFe': 44,   # Chave de acesso do MDFe
            'NFSe': 15,   # Número da NFSe (varia por município, mas geralmente 15 dígitos)
            'CTeOS': 44,  # CTe para serviços
            'SAT': 44,    # Cupom Fiscal Eletrônico (SAT)
        }
        
        # Verificar se o tipo é suportado
        tipo = tipo.upper()
        if tipo not in comprimentos:
            tipos_suportados = ', '.join(comprimentos.keys())
            return False, f'Tipo de documento fiscal não suportado. Tipos suportados: {tipos_suportados}', ''
        
        comprimento_esperado = comprimentos[tipo]
        
        # Verificar comprimento
        if len(numero_limpo) != comprimento_esperado:
            return False, f'Número do {tipo} deve ter {comprimento_esperado} dígitos', ''
            
        # Formatar o número de acordo com o tipo
        if tipo in ['NFe', 'NFCe', 'CTe', 'MDFe', 'CTeOS', 'SAT'] and comprimento_esperado == 44:
            # Formatar chave de acesso: 44 dígitos agrupados como 4-2-3-4-1-9-9-13-1-1-4
            partes = [
                numero_limpo[0:4],   # Código da UF + Ano/Mês
                numero_limpo[4:6],   # CNPJ emitente (2 primeiros dígitos)
                numero_limpo[6:9],   # Modelo + Série
                numero_limpo[9:13],  # Número
                numero_limpo[13:14],  # Tipo de emissão
                numero_limpo[14:23],  # Código numérico
                numero_limpo[23:32],  # DV
                numero_limpo[32:45]   # Código de barras
            ]
            numero_formatado = '-'.join(partes)
        else:
            # Para outros tipos, apenas retorna o número limpo
            numero_formatado = numero_limpo
            
        return True, f'{tipo} válido', numero_formatado
    
    def _validar_codigo_barras(self, codigo: str, tipo: str = 'geral') -> tuple[bool, str, str]:
        """
        Valida e formata códigos de barras (boletos, DARF, GRU, etc.)
        
        Args:
            codigo: Código de barras a ser validado
            tipo: Tipo de código de barras ('geral', 'boleto_bancario', 'convenio', 'darj', 'darf')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, codigo_formatado: str)
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código de barras não pode estar vazio', ''
            
        # Remover caracteres não numéricos
        codigo_limpo = ''.join(filter(str.isdigit, str(codigo)))
        
        # Mapear tipos de código de barras para seus respectivos comprimentos
        comprimentos = {
            'geral': None,           # Sem validação de comprimento
            'boleto_bancario': 44,   # Código de barras padrão bancário
            'boleto_arrecadacao': 44, # Código de barras de arrecadação
            'convenio': 48,          # Código de convênio (ex: contas de água, luz)
            'darj': 48,              # DARJ - Documento de Arrecadação de Receitas Judiciais
            'darf': 36,              # DARF - Documento de Arrecadação de Receitas Federais
            'gps': 15,               # GPS - Guia da Previdência Social
            'iptu': 11,              # IPTU - Imposto Predial e Territorial Urbano
            'iptu_parcelado': 15,    # IPTU Parcelado
            'iptu_completo': 17      # IPTU Completo (incluindo dígito verificador)
        }
        
        # Verificar se o tipo é suportado
        tipo = tipo.lower()
        if tipo not in comprimentos:
            tipos_suportados = ', '.join(comprimentos.keys())
            return False, f'Tipo de código de barras não suportado. Tipos suportados: {tipos_suportados}', ''
        
        comprimento_esperado = comprimentos[tipo]
        
        # Validar comprimento se especificado
        if comprimento_esperado and len(codigo_limpo) != comprimento_esperado:
            return False, f'Código de barras do tipo {tipo} deve ter {comprimento_esperado} dígitos', ''
        
        # Formatar o código de acordo com o tipo
        if tipo == 'boleto_bancario' and len(codigo_limpo) == 44:
            # Formato: 11111.11111 11111.111111 11111.111111 1 11111111111111
            partes = [
                codigo_limpo[0:5],     # Código do banco + moeda + DV
                codigo_limpo[5:10],    # Fator de vencimento
                codigo_limpo[10:15],   # Valor
                codigo_limpo[15:21],   # Campo livre (parte 1)
                codigo_limpo[21:27],   # Campo livre (parte 2)
                codigo_limpo[27:33],   # Campo livre (parte 3)
                codigo_limpo[33:34],   # DV do código de barras
                codigo_limpo[34:44]     # Valor
            ]
            codigo_formatado = (
                f'{partes[0]}.{partes[1]} {partes[2]}.{partes[3]} '
                f'{partes[4]}.{partes[5]} {partes[6]} {partes[7]}'
            )
        elif tipo in ['darf', 'darj'] and len(codigo_limpo) in [36, 48]:
            # Formatar DARF/DARJ com separadores a cada 4 dígitos
            codigo_formatado = ' '.join([codigo_limpo[i:i+4] for i in range(0, len(codigo_limpo), 4)])
        else:
            # Formato genérico: agrupa em blocos de 5 dígitos
            codigo_formatado = ' '.join([codigo_limpo[i:i+5] for i in range(0, len(codigo_limpo), 5)])
            
        return True, 'Código de barras válido', codigo_formatado
    
    def _validar_inscricao_estadual(self, ie: str, uf: str) -> tuple[bool, str, str]:
        """
        Valida uma Inscrição Estadual (IE) de acordo com as regras do estado
        
        Args:
            ie: Número da Inscrição Estadual
            uf: Sigla do estado (ex: SP, RJ, MG, etc.)
            
        Retorna:
            tuple: (valido: bool, mensagem: str, ie_formatada: str)
        """
        if not ie or not str(ie).strip():
            return False, 'Inscrição Estadual não pode estar vazia', ''
            
        if not uf or len(uf.strip()) != 2:
            return False, 'UF inválida. Informe a sigla do estado com 2 caracteres', ''
            
        # Remover caracteres não alfanuméricos e converter para maiúsculas
        ie_limpa = re.sub(r'[^a-zA-Z0-9]', '', str(ie)).upper()
        uf = uf.strip().upper()
        
        # Mapeamento de funções de validação por estado
        validadores = {
            'AC': self._validar_ie_ac,
            'AL': self._validar_ie_al,
            'AP': self._validar_ie_ap,
            'AM': self._validar_ie_am,
            'BA': self._validar_ie_ba,
            'CE': self._validar_ie_ce,
            'DF': self._validar_ie_df,
            'ES': self._validar_ie_es,
            'GO': self._validar_ie_go,
            'MA': self._validar_ie_ma,
            'MT': self._validar_ie_mt,
            'MS': self._validar_ie_ms,
            'MG': self._validar_ie_mg,
            'PA': self._validar_ie_pa,
            'PB': self._validar_ie_pb,
            'PR': self._validar_ie_pr,
            'PE': self._validar_ie_pe,
            'PI': self._validar_ie_pi,
            'RJ': self._validar_ie_rj,
            'RN': self._validar_ie_rn,
            'RS': self._validar_ie_rs,
            'RO': self._validar_ie_ro,
            'RR': self._validar_ie_rr,
            'SC': self._validar_ie_sc,
            'SP': self._validar_ie_sp,
            'SE': self._validar_ie_se,
            'TO': self._validar_ie_to
        }
        
        # Verificar se a UF tem validador implementado
        if uf not in validadores:
            return False, f'Validação de IE para a UF {uf} não implementada', ie_limpa
            
        # Chamar o validador específico do estado
        valido, mensagem, ie_formatada = validadores[uf](ie_limpa)
        
        return valido, mensagem, ie_formatada
    
    # Métodos auxiliares para validação de IE por estado
    def _validar_ie_sp(self, ie: str) -> tuple[bool, str, str]:
        """Valida Inscrição Estadual de São Paulo"""
        # Implementação simplificada - em produção, implementar a lógica completa
        if len(ie) not in [12, 13]:
            return False, 'IE de SP deve ter 12 ou 13 dígitos', ''
        # TODO: Implementar validação completa do dígito verificador
        return True, 'IE de SP válida', f'{ie[:3]}.{ie[3:6]}.{ie[6:9]}.{ie[9:]}'
    
    def _validar_ie_rj(self, ie: str) -> tuple[bool, str, str]:
        """Valida Inscrição Estadual do Rio de Janeiro"""
        if len(ie) != 8:
            return False, 'IE do RJ deve ter 8 dígitos', ''
        # TODO: Implementar validação completa do dígito verificador
        return True, 'IE do RJ válida', f'{ie[:2]}.{ie[2:5]}.{ie[5:]}'
    
    # Adicione os demais métodos de validação por estado aqui...
    # _validar_ie_mg, _validar_ie_es, etc.
    
    def _formatar_ie_por_uf(self, ie: str, uf: str) -> str:
        """
        Formata uma Inscrição Estadual de acordo com as regras do estado
        
        Args:
            ie: Número da Inscrição Estadual (apenas dígitos)
            uf: Sigla do estado (ex: SP, RJ, MG, etc.)
            
        Retorna:
            str: IE formatada
        """
        if not ie or not uf:
            return ie
            
        uf = uf.strip().upper()
        
        # Mapeamento de formatação por estado
        formatos = {
            'AC': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}/{x[8:9]}-{x[9:]}',  # 01.004.823/001-12
            'AL': lambda x: f'{x[:2]}{x[2:6]}{x[6:8]}',                     # 240000048
            'AP': lambda x: f'{x[:2]}.{x[2:4]}.{x[4:6]}-{x[6:]}',            # 03.012.345-9
            'AM': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:]}',            # 04.345.678-9
            'BA': lambda x: f'{x[:6]}-{x[6:8]}',                             # 123456-63
            'CE': lambda x: f'{x[:8]}-{x[8:9]}',                             # 12345678-9
            'DF': lambda x: f'{x[:4]}.{x[4:6]}.{x[6:9]}.{x[9:11]}-{x[11:12]}', # 07.345.678.901-50
            'ES': lambda x: f'{x[:3]}.{x[3:6]}.{x[6:8]}.{x[8:9]}',           # 123.456.78-9
            'GO': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:9]}',           # 10.987.654-7
            'MA': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}',                    # 12.345.678
            'MT': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}',                    # 0013000001-9
            'MS': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}',                    # 28.123.456
            'MG': lambda x: f'{x[:3]}.{x[3:6]}.{x[6:9]}/{x[9:13]}-{x[13:]}',  # 062.307.904/0081
            'PA': lambda x: f'{x[:2]}-{x[2:9]}-{x[9:10]}',                   # 15-999999-5
            'PB': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:9]}',           # 16.004.017-5
            'PR': lambda x: f'{x[:3]}.{x[3:7]}.{x[7:9]}-{x[9:10]}',          # 123.45678-50
            'PE': lambda x: f'{x[:3]}.{x[3:6]}.{x[6:8]}',                    # 0321418-40
            'PI': lambda x: f'{x[:2]}.{x[2:8]}-{x[8:9]}',                    # 19.301656-7
            'RJ': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}',                    # 99.999.99
            'RN': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:9]}' if len(x) == 9 else f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:9]}{x[9:10]}',  # 20.040.040-1 ou 20.0.040.040-0
            'RS': lambda x: f'{x[:3]}/{x[3:7]}{x[7:8]}',                     # 224/3658792
            'RO': lambda x: f'{x[:3]}.{x[3:5]}.{x[5:9]}-{x[9:10]}',          # 101.62521-3
            'RR': lambda x: f'{x[:2]}.{x[2:5]}.{x[5:8]}-{x[8:9]}',           # 24006628-1
            'SC': lambda x: f'{x[:3]}.{x[3:6]}.{x[6:9]}',                    # 251.040.852
            'SP': lambda x: f'{x[:3]}.{x[3:6]}.{x[6:9]}.{x[9:12]}',          # 110.042.490.114
            'SE': lambda x: f'{x[:2]}.{x[2:8]}-{x[8:9]}',                    # 27.123456-8
            'TO': lambda x: f'{x[:2]}.{x[2:8]}.{x[8:9]}-{x[9:10]}'           # 29.01.022783-6
        }
        
        # Aplicar formatação se existir para o estado
        if uf in formatos:
            try:
                return formatos[uf](ie)
            except (IndexError, ValueError):
                # Se ocorrer erro na formatação, retorna sem formatação
                return ie
                
        # Se não houver formatação específica, retorna sem formatação
        return ie
    
    def _validar_codigo_tributo(self, codigo: str, tipo: str = 'PIS') -> tuple[bool, str, str]:
        """
        Valida códigos de tributos e contribuições federais
        
        Args:
            codigo: Código a ser validado
            tipo: Tipo de tributo/contribuição ('PIS', 'COFINS', 'IRPJ', 'CSLL', 'INSS', 'ICMS')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, codigo_formatado: str)
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código não pode estar vazio', ''
            
        # Remover caracteres não numéricos
        codigo_limpo = ''.join(filter(str.isdigit, str(codigo)))
        
        # Mapeamento de tipos de códigos para seus respectivos comprimentos
        comprimentos = {
            'PIS': 11,      # PIS/PASEP/NIT
            'COFINS': 14,   # COFINS
            'IRPJ': 14,     # IRPJ
            'CSLL': 14,     # CSLL
            'INSS': 12,     # INSS - Número de Inscrição do INSS
            'ICMS': 13,     # Inscrição Estadual (ICMS) - pode variar por estado
            'NCM': 8,       # NCM - Nomenclatura Comum do Mercosul
            'CEST': 7,      # Código Especificador da Substituição Tributária
            'CNAE': 7,      # Classificação Nacional de Atividades Econômicas
            'CIDE': 14,     # Contribuição de Intervenção no Domínio Econômico
            'CPRB': 14,     # Contribuição Previdenciária sobre a Receita Bruta
            'SUSPENS': 14   # Número de suspensão do processo administrativo fiscal
        }
        
        # Verificar se o tipo é suportado
        tipo = tipo.upper()
        if tipo not in comprimentos:
            tipos_suportados = ', '.join(comprimentos.keys())
            return False, f'Tipo de código não suportado. Tipos suportados: {tipos_suportados}', ''
        
        comprimento_esperado = comprimentos[tipo]
        
        # Verificar comprimento
        if len(codigo_limpo) != comprimento_esperado:
            return False, f'Código {tipo} deve ter {comprimento_esperado} dígitos', ''
            
        # Validações específicas por tipo
        if tipo == 'PIS':
            # Validação do PIS/PASEP/NIT (11 dígitos)
            if not self._validar_digito_verificador_pis(codigo_limpo):
                return False, 'Dígito verificador do PIS/PASEP/NIT inválido', ''
                
            # Formatar: 123.45678.90-1
            codigo_formatado = f'{codigo_limpo[:3]}.{codigo_limpo[3:8]}.{codigo_limpo[8:10]}-{codigo_limpo[10:]}'
            
        elif tipo == 'NCM':
            # Formatar NCM: 1234.56.78
            codigo_formatado = f'{codigo_limpo[:4]}.{codigo_limpo[4:6]}.{codigo_limpo[6:8]}'
            
        elif tipo == 'CEST':
            # Formatar CEST: 12.345.67
            codigo_formatado = f'{codigo_limpo[:2]}.{codigo_limpo[2:5]}.{codigo_limpo[5:7]}'
            
        elif tipo == 'CNAE':
            # Formatar CNAE: 1234-5/99
            codigo_formatado = f'{codigo_limpo[:4]}-{codigo_limpo[4:5]}/{codigo_limpo[5:7]}'
            
        else:
            # Formato genérico para outros códigos
            if len(codigo_limpo) > 4:
                codigo_formatado = f'{codigo_limpo[:2]}.{codigo_limpo[2:5]}.{codigo_limpo[5:8]}'
                if len(codigo_limpo) > 8:
                    codigo_formatado += f'.{codigo_limpo[8:12]}'
                    if len(codigo_limpo) > 12:
                        codigo_formatado += f'-{codigo_limpo[12:]}'
            else:
                codigo_formatado = codigo_limpo
        
        return True, f'Código {tipo} válido', codigo_formatado
    
    def _validar_digito_verificador_pis(self, pis: str) -> bool:
        """
        Valida o dígito verificador do PIS/PASEP/NIT
        
        Args:
            pis: Número do PIS/PASEP/NIT sem formatação (11 dígitos)
            
        Retorna:
            bool: True se o dígito verificador for válido, False caso contrário
        """
        if not pis or len(pis) != 11 or not pis.isdigit():
            return False
            
        # Peso para cada dígito (de 3 a 10, depois 2)
        pesos = [3, 2, 9, 8, 7, 6, 5, 4, 3, 2]
        
        # Calcular o dígito verificador
        soma = 0
        for i in range(10):
            soma += int(pis[i]) * pesos[i]
            
        resto = soma % 11
        digito_verificador = 11 - resto if resto > 1 else 0
        
        # Verificar se o dígito verificador calculado é igual ao informado
        return digito_verificador == int(pis[10])
    
    def _validar_processo_administrativo(self, numero: str, orgao: str = 'RFB') -> tuple[bool, str, str]:
        """
        Valida e formata números de processos administrativos fiscais
        
        Args:
            numero: Número do processo administrativo
            orgao: Órgão responsável ('RFB', 'CARF', 'PGFN', 'Sefaz', 'Prefeitura')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, numero_formatado: str)
        """
        if not numero or not str(numero).strip():
            return False, 'Número do processo não pode estar vazio', ''
            
        # Remover caracteres não alfanuméricos
        numero_limpo = re.sub(r'[^a-zA-Z0-9]', '', str(numero)).upper()
        orgao = orgao.upper()
        
        # Mapeamento de formatos por órgão
        formatos = {
            'RFB': {
                'descricao': 'Receita Federal do Brasil',
                'padrao': r'^(\d{4})(\d{2})(\d{4})(\d{1})(\d{4})(\d{1})$',
                'formato': '00000.000000/0000-00',
                'exemplo': '12345.678901/2019-99'
            },
            'CARF': {
                'descricao': 'Conselho Administrativo de Recursos Fiscais',
                'padrao': r'^(\d{5})[\-\.]?(\d{6})[\-\.]?(\d{4})[\-\.]?(\d{2})$',
                'formato': '00000-000000/0000-00',
                'exemplo': '12345-678901/2019-99'
            },
            'PGFN': {
                'descricao': 'Procuradoria-Geral da Fazenda Nacional',
                'padrao': r'^(\d{4})(\d{2})(\d{4})(\d{1})(\d{4})(\d{1})$',
                'formato': '00000.000000/0000-00',
                'exemplo': '12345.678901/2019-99'
            },
            'SEFAZ': {
                'descricao': 'Secretaria da Fazenda Estadual',
                'padrao': r'^(\d{4})[\-\.]?(\d{3})[\-\.]?(\d{4})[\-\.]?(\d{4})$',
                'formato': '0000.000.0000-0',
                'exemplo': '1234.567.8901-2'
            },
            'PREFEITURA': {
                'descricao': 'Prefeitura Municipal',
                'padrao': r'^(\d{4})[\-\.]?(\d{6})[\-\.]?(\d{1})$',
                'formato': '0000.000000-0',
                'exemplo': '2023.123456-7'
            }
        }
        
        # Verificar se o órgão é suportado
        if orgao not in formatos:
            orgaos_suportados = ', '.join(formatos.keys())
            return False, f'Órgão não suportado. Órgãos suportados: {orgaos_suportados}', ''
        
        padrao = formatos[orgao]['padrao']
        
        # Verificar se o número corresponde ao padrão
        import re
        match = re.match(padrao, numero_limpo)
        
        if not match:
            return False, f'Número de processo inválido para {formatos[orgao]["descricao"]}. Formato esperado: {formatos[orgao]["formato"]}', ''
        
        # Formatar o número de acordo com o órgão
        if orgao == 'RFB' or orgao == 'PGFN':
            # Formato: 12345.678901/2019-99
            numero_formatado = f'{match.group(1)}{match.group(2)}.{match.group(3)}{match.group(4)}.{match.group(5)}-{match.group(6)}'
        elif orgao == 'CARF':
            # Formato: 12345-678901/2019-99
            numero_formatado = f'{match.group(1)}-{match.group(2)}/{match.group(3)}-{match.group(4)}'
        elif orgao == 'SEFAZ':
            # Formato: 1234.567.8901-2
            numero_formatado = f'{match.group(1)}.{match.group(2)}.{match.group(3)}-{match.group(4)}'
        elif orgao == 'PREFEITURA':
            # Formato: 2023.123456-7
            numero_formatado = f'{match.group(1)}.{match.group(2)}-{match.group(3)}'
        else:
            # Formato genérico
            numero_formatado = numero_limpo
        
        return True, f'Processo administrativo {formatos[orgao]["descricao"]} válido', numero_formatado
    
    def _validar_codigo_autenticacao(self, codigo: str, tipo: str = 'NFe') -> tuple[bool, str, str]:
        """
        Valida códigos de autenticação de documentos fiscais
        
        Args:
            codigo: Código de autenticação a ser validado
            tipo: Tipo de documento fiscal ('NFe', 'NFCe', 'CTe', 'MDFe', 'NFSe', 'SAT')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, codigo_formatado: str)
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código de autenticação não pode estar vazio', ''
            
        # Remover caracteres não alfanuméricos e converter para maiúsculas
        codigo_limpo = re.sub(r'[^a-zA-Z0-9]', '', str(codigo)).upper()
        tipo = tipo.upper()
        
        # Mapeamento de tipos de códigos para seus respectivos formatos
        formatos = {
            'NFE': {
                'descricao': 'Chave de Acesso da NF-e',
                'tamanho': 44,
                'formato': 'NFe+Chave com 44 dígitos',
                'exemplo': 'NFe12345678901234567890123456789012345678901234'
            },
            'NFCE': {
                'descricao': 'Chave de Acesso da NFC-e',
                'tamanho': 44,
                'formato': 'NFCe+Chave com 44 dígitos',
                'exemplo': 'NFCe12345678901234567890123456789012345678901234'
            },
            'CTE': {
                'descricao': 'Chave de Acesso do CT-e',
                'tamanho': 44,
                'formato': 'CTe+Chave com 44 dígitos',
                'exemplo': 'CTe12345678901234567890123456789012345678901234'
            },
            'MDFE': {
                'descricao': 'Chave de Acesso do MDF-e',
                'tamanho': 44,
                'formato': 'MDFe+Chave com 44 dígitos',
                'exemplo': 'MDFe12345678901234567890123456789012345678901234'
            },
            'NFSE': {
                'descricao': 'Código de Verificação da NFSe',
                'tamanho': 36,
                'formato': 'Código alfanumérico de 36 caracteres',
                'exemplo': 'A1B2C3D4-E5F6-4G7H-8I9J-0K1L2M3N4O5P'
            },
            'SAT': {
                'descricao': 'Código de Ativação do SAT',
                'tamanho': 32,
                'formato': 'Código alfanumérico de 32 caracteres',
                'exemplo': '12345678901234567890123456789012'
            },
            'QRCODE': {
                'descricao': 'Código QR Code',
                'tamanho': None,  # Tamanho variável
                'formato': 'URL ou dados do QR Code',
                'exemplo': 'https://www.sefaz.xx.gov.br/nfce/qrcode?p=12345678901234567890123456789012345678901234'
            }
        }
        
        # Verificar se o tipo é suportado
        if tipo not in formatos:
            tipos_suportados = ', '.join(formatos.keys())
            return False, f'Tipo de código de autenticação não suportado. Tipos suportados: {tipos_suportados}', ''
        
        formato = formatos[tipo]
        
        # Verificar comprimento se especificado
        if formato['tamanho'] and len(codigo_limpo) != formato['tamanho']:
            return False, f'Código de autenticação {formato["descricao"]} deve ter {formato["tamanho"]} caracteres', ''
        
        # Validações específicas por tipo
        if tipo in ['NFE', 'NFCE', 'CTE', 'MDFE']:
            # Verificar se começa com o prefixo do documento
            prefixo = tipo
            if not codigo_limpo.startswith(prefixo):
                codigo_limpo = f"{tipo}{codigo_limpo}"
                
            # Verificar se tem o tamanho correto após adicionar o prefixo
            if len(codigo_limpo) != len(prefixo) + 44:
                return False, f'Chave de acesso {formato["descricao"]} inválida', ''
                
            # Formatar chave de acesso em grupos
            chave = codigo_limpo[len(prefixo):]
            codigo_formatado = f"{prefixo} {chave[:4]} {chave[4:8]} {chave[8:12]} {chave[12:16]} {chave[16:20]} {chave[20:24]} {chave[24:28]} {chave[28:32]} {chave[32:36]} {chave[36:40]} {chave[40:]}"
            
        elif tipo == 'NFSE':
            # Formatar código de verificação com hifens
            codigo_formatado = '-'.join([codigo_limpo[i:i+8] for i in range(0, len(codigo_limpo), 8)])
            
        elif tipo == 'SAT':
            # Manter o código sem formatação
            codigo_formatado = codigo_limpo
            
        elif tipo == 'QRCODE':
            # Verificar se é uma URL válida
            from urllib.parse import urlparse
            try:
                result = urlparse(codigo_limpo)
                if not all([result.scheme, result.netloc]):
                    return False, 'URL do QR Code inválida', ''
                codigo_formatado = codigo_limpo
            except ValueError:
                return False, 'Formato de QR Code inválido', ''
        
        return True, f'Código de autenticação {formato["descricao"]} válido', codigo_formatado
    
    def _validar_regime_tributario(self, codigo: str, tipo: str = 'SPED') -> tuple[bool, str, dict]:
        """
        Valida e retorna informações sobre códigos de regime tributário
        
        Args:
            codigo: Código do regime tributário
            tipo: Tipo de código ('SPED', 'EFD', 'SIMEI', 'SIMPLES_NACIONAL', 'LUCRO_PRESUMIDO', 'LUCRO_REAL')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, info: dict)
                info contém:
                - codigo: código formatado
                - descricao: descrição do regime tributário
                - tipo: tipo do regime tributário
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código de regime tributário não pode estar vazio', {}
            
        # Remover caracteres não numéricos
        codigo_limpo = ''.join(filter(str.isdigit, str(codigo)))
        tipo = tipo.upper()
        
        # Mapeamento de códigos de regime tributário
        regimes = {
            # SPED Fiscal (Regime Tributação)
            '1': {'descricao': 'Simples Nacional', 'tipo': 'SIMPLES_NACIONAL'},
            '2': {'descricao': 'Simples Nacional - excesso de sublimite de receita bruta', 'tipo': 'SIMPLES_NACIONAL'},
            '3': {'descricao': 'Regime Normal', 'tipo': 'LUCRO_REAL'},
            '4': {'descricao': 'Simples Nacional - MEI', 'tipo': 'SIMPLES_NACIONAL'},
            '5': {'descricao': 'Simples Nacional - MEI - excesso de sublimite de receita bruta', 'tipo': 'SIMPLES_NACIONAL'},
            
            # EFD Contribuições (Tipo de Controle)
            '00': {'descricao': 'Contribuição Normal', 'tipo': 'NORMAL'},
            '01': {'descricao': 'Substituição Tributária', 'tipo': 'ST'},
            '02': {'descricao': 'Isenção', 'tipo': 'ISENCAO'},
            '03': {'descricao': 'Imune', 'tipo': 'IMUNE'},
            '04': {'descricao': 'Suspensão', 'tipo': 'SUSPENSAO'},
            '05': {'descricao': 'Não Incidência', 'tipo': 'NAO_INCIDENCIA'},
            '06': {'descricao': 'Imunidade', 'tipo': 'IMUNIDADE'},
            '07': {'descricao': 'Suspensão por Decisão Judicial', 'tipo': 'SUSPENSAO_JUDICIAL'},
            
            # Regime de Tributação Especial (RT)
            '101': {'descricao': 'Regime de Caixa', 'tipo': 'CAIXA'},
            '102': {'descricao': 'Regime de Competência', 'tipo': 'COMPETENCIA'},
            '103': {'descricao': 'Regime de Caixa e Competência', 'tipo': 'MISTO'},
            
            # Códigos de Regime de Tributação Específicos
            '1': {'descricao': 'Microempresa', 'tipo': 'SIMPLES_NACIONAL'},
            '2': {'descricao': 'Empresa de Pequeno Porte', 'tipo': 'SIMPLES_NACIONAL'},
            '3': {'descricao': 'Demais', 'tipo': 'OUTROS'},
            '4': {'descricao': 'Microempreendedor Individual', 'tipo': 'MEI'},
            '5': {'descricao': 'Microempreendedor Individual - Produtor Rural', 'tipo': 'MEI_RURAL'},
            '6': {'descricao': 'Microempreendedor Individual - Comércio', 'tipo': 'MEI_COMERCIO'},
            '7': {'descricao': 'Microempreendedor Individual - Indústria', 'tipo': 'MEI_INDUSTRIA'},
            '8': {'descricao': 'Microempreendedor Individual - Serviços', 'tipo': 'MEI_SERVICOS'},
            '9': {'descricao': 'Microempreendedor Individual - Serviços de Comércio', 'tipo': 'MEI_COMERCIO_SERVICOS'}
        }
        
        # Tipos de códigos aceitos
        tipos_aceitos = ['SPED', 'EFD', 'SIMEI', 'SIMPLES_NACIONAL', 'LUCRO_PRESUMIDO', 'LUCRO_REAL']
        
        # Verificar se o tipo é suportado
        if tipo not in tipos_aceitos:
            return False, f'Tipo de código de regime tributário não suportado. Tipos suportados: {", ".join(tipos_aceitos)}', {}
        
        # Verificar se o código existe no mapeamento
        if codigo_limpo not in regimes:
            return False, 'Código de regime tributário inválido', {}
        
        # Obter informações do regime
        info = regimes[codigo_limpo]
        
        # Verificar se o tipo do regime corresponde ao tipo informado
        if tipo != 'SPED' and tipo != 'EFD':  # SPED e EFD são genéricos
            if info['tipo'] != tipo:
                return False, f'Código não corresponde ao tipo de regime tributário informado: {tipo}', {}
        
        # Formatar o código para retorno
        codigo_formatado = codigo_limpo.zfill(2)  # Padroniza com 2 dígitos
        
        # Montar objeto de retorno com informações adicionais
        info_retorno = {
            'codigo': codigo_formatado,
            'descricao': info['descricao'],
            'tipo': info['tipo']
        }
        
        return True, f'Regime tributário válido: {info["descricao"]}', info_retorno
    
    def _validar_situacao_documento(self, codigo: str, tipo_documento: str = 'NFE') -> tuple[bool, str, dict]:
        """
        Valida e retorna informações sobre códigos de situação de documentos fiscais
        
        Args:
            codigo: Código da situação do documento
            tipo_documento: Tipo de documento fiscal ('NFE', 'NFCE', 'CTE', 'MDFE', 'NFSE')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, info: dict)
                info contém:
                - codigo: código formatado
                - descricao: descrição da situação
                - status: status principal (AUTORIZADO, CANCELADO, DENEGADO, etc.)
                - pode_utilizar: se o documento pode ser utilizado
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código de situação não pode estar vazio', {}
            
        # Remover caracteres não numéricos
        codigo_limpo = ''.join(filter(str.isdigit, str(codigo)))
        tipo_documento = tipo_documento.upper()
        
        # Mapeamento de códigos de situação por tipo de documento
        situacoes = {
            'NFE': {
                '100': {'descricao': 'Autorizado o uso da NF-e', 'status': 'AUTORIZADO', 'pode_utilizar': True},
                '101': {'descricao': 'Cancelamento de NF-e homologado', 'status': 'CANCELADO', 'pode_utilizar': False},
                '102': {'descricao': 'Inutilização de número homologado', 'status': 'INUTILIZADO', 'pode_utilizar': False},
                '103': {'descricao': 'Lote recebido com sucesso', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '104': {'descricao': 'Lote processado', 'status': 'PROCESSADO', 'pode_utilizar': False},
                '105': {'descricao': 'Lote em processamento', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '106': {'descricao': 'Lote não localizado', 'status': 'ERRO', 'pode_utilizar': False},
                '107': {'descricao': 'Serviço em Operação', 'status': 'EM_OPERACAO', 'pode_utilizar': False},
                '108': {'descricao': 'Serviço Paralisado Momentaneamente', 'status': 'PARALISADO', 'pode_utilizar': False},
                '109': {'descricao': 'Serviço Paralisado sem Previsão', 'status': 'PARALISADO', 'pode_utilizar': False},
                '110': {'descricao': 'Uso Denegado', 'status': 'DENEGADO', 'pode_utilizar': False},
                '135': {'descricao': 'Evento registrado e vinculado a NF-e', 'status': 'EVENTO_VINCULADO', 'pode_utilizar': True},
                '136': {'descricao': 'Evento registrado, mas não vinculado a NF-e', 'status': 'EVENTO_NAO_VINCULADO', 'pode_utilizar': True},
                '150': {'descricao': 'Autorizado fora do prazo', 'status': 'AUTORIZADO_FORA_PRAZO', 'pode_utilizar': True},
                '151': {'descricao': 'Cancelado fora do prazo', 'status': 'CANCELADO_FORA_PRAZO', 'pode_utilizar': False},
                '155': {'descricao': 'Denegado', 'status': 'DENEGADO', 'pode_utilizar': False},
                '201': {'descricao': 'Rejeição: Número máximo de numeração de inutilização de NF esgotado', 'status': 'ERRO', 'pode_utilizar': False},
                '202': {'descricao': 'Rejeição: Falha no cancelamento/chave de acesso já está cancelada', 'status': 'ERRO', 'pode_utilizar': False},
                '204': {'descricao': 'Rejeição: NF-e não consta na base de dados da SEFAZ', 'status': 'ERRO', 'pode_utilizar': False},
                '205': {'descricao': 'Rejeição: NF-e já está cancelada na base de dados da SEFAZ', 'status': 'ERRO', 'pode_utilizar': False},
                '301': {'descricao': 'Uso Denegado: Irregularidade fiscal do emitente', 'status': 'DENEGADO', 'pode_utilizar': False},
                '302': {'descricao': 'Uso Denegado: Irregularidade fiscal do destinatário', 'status': 'DENEGADO', 'pode_utilizar': False},
                '303': {'descricao': 'Uso Denegado: Destinatário não habilitado a operar na UF', 'status': 'DENEGADO', 'pode_utilizar': False}
            },
            'NFCE': {
                # Mesmos códigos da NFe, mas com mensagens específicas para NFCe
                '100': {'descricao': 'Autorizado o uso da NFC-e', 'status': 'AUTORIZADO', 'pode_utilizar': True},
                '101': {'descricao': 'Cancelamento de NFC-e homologado', 'status': 'CANCELADO', 'pode_utilizar': False},
                '110': {'descricao': 'Uso Denegado', 'status': 'DENEGADO', 'pode_utilizar': False},
                '150': {'descricao': 'Autorizado fora do prazo', 'status': 'AUTORIZADO_FORA_PRAZO', 'pode_utilizar': True},
                '151': {'descricao': 'Cancelado fora do prazo', 'status': 'CANCELADO_FORA_PRAZO', 'pode_utilizar': False},
                '201': {'descricao': 'Rejeição: Número máximo de numeração de inutilização de NFC-e esgotado', 'status': 'ERRO', 'pode_utilizar': False},
                '202': {'descricao': 'Rejeição: Falha no cancelamento/chave de acesso já está cancelada', 'status': 'ERRO', 'pode_utilizar': False},
                '204': {'descricao': 'Rejeição: NFC-e não consta na base de dados da SEFAZ', 'status': 'ERRO', 'pode_utilizar': False},
                '205': {'descricao': 'Rejeição: NFC-e já está cancelada na base de dados da SEFAZ', 'status': 'ERRO', 'pode_utilizar': False},
                '301': {'descricao': 'Uso Denegado: Irregularidade fiscal do emitente', 'status': 'DENEGADO', 'pode_utilizar': False},
                '302': {'descricao': 'Uso Denegado: Irregularidade fiscal do destinatário', 'status': 'DENEGADO', 'pode_utilizar': False},
                '303': {'descricao': 'Uso Denegado: Destinatário não habilitado a operar na UF', 'status': 'DENEGADO', 'pode_utilizar': False}
            },
            'CTE': {
                '100': {'descricao': 'Autorizado o uso do CT-e', 'status': 'AUTORIZADO', 'pode_utilizar': True},
                '101': {'descricao': 'Cancelamento de CT-e homologado', 'status': 'CANCELADO', 'pode_utilizar': False},
                '102': {'descricao': 'Inutilização de número homologado', 'status': 'INUTILIZADO', 'pode_utilizar': False},
                '103': {'descricao': 'Lote recebido com sucesso', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '104': {'descricao': 'Lote processado', 'status': 'PROCESSADO', 'pode_utilizar': False},
                '105': {'descricao': 'Lote em processamento', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '106': {'descricao': 'Lote não localizado', 'status': 'ERRO', 'pode_utilizar': False},
                '107': {'descricao': 'Serviço em Operação', 'status': 'EM_OPERACAO', 'pode_utilizar': False},
                '108': {'descricao': 'Serviço Paralisado Momentaneamente', 'status': 'PARALISADO', 'pode_utilizar': False},
                '109': {'descricao': 'Serviço Paralisado sem Previsão', 'status': 'PARALISADO', 'pode_utilizar': False},
                '110': {'descricao': 'Uso Denegado', 'status': 'DENEGADO', 'pode_utilizar': False},
                '135': {'descricao': 'Evento registrado e vinculado ao CT-e', 'status': 'EVENTO_VINCULADO', 'pode_utilizar': True},
                '136': {'descricao': 'Evento registrado, mas não vinculado ao CT-e', 'status': 'EVENTO_NAO_VINCULADO', 'pode_utilizar': True}
            },
            'MDFE': {
                '100': {'descricao': 'Autorizado o uso do MDF-e', 'status': 'AUTORIZADO', 'pode_utilizar': True},
                '101': {'descricao': 'Cancelamento de MDF-e homologado', 'status': 'CANCELADO', 'pode_utilizar': False},
                '102': {'descricao': 'Inutilização de número homologado', 'status': 'INUTILIZADO', 'pode_utilizar': False},
                '103': {'descricao': 'Lote recebido com sucesso', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '104': {'descricao': 'Lote processado', 'status': 'PROCESSADO', 'pode_utilizar': False},
                '105': {'descricao': 'Lote em processamento', 'status': 'EM_PROCESSAMENTO', 'pode_utilizar': False},
                '106': {'descricao': 'Lote não localizado', 'status': 'ERRO', 'pode_utilizar': False},
                '107': {'descricao': 'Serviço em Operação', 'status': 'EM_OPERACAO', 'pode_utilizar': False},
                '108': {'descricao': 'Serviço Paralisado Momentaneamente', 'status': 'PARALISADO', 'pode_utilizar': False},
                '109': {'descricao': 'Serviço Paralisado sem Previsão', 'status': 'PARALISADO', 'pode_utilizar': False},
                '110': {'descricao': 'Uso Denegado', 'status': 'DENEGADO', 'pode_utilizar': False},
                '135': {'descricao': 'Evento registrado e vinculado ao MDF-e', 'status': 'EVENTO_VINCULADO', 'pode_utilizar': True},
                '136': {'descricao': 'Evento registrado, mas não vinculado ao MDF-e', 'status': 'EVENTO_NAO_VINCULADO', 'pode_utilizar': True}
            },
            'NFSE': {
                '1': {'descricao': 'Nula', 'status': 'NULA', 'pode_utilizar': False},
                '2': {'descricao': 'Normal', 'status': 'NORMAL', 'pode_utilizar': True},
                '3': {'descricao': 'Cancelada', 'status': 'CANCELADA', 'pode_utilizar': False},
                '4': {'descricao': 'Cancelada a pedido do tomador', 'status': 'CANCELADA_TOMADOR', 'pode_utilizar': False},
                '5': {'descricao': 'Cancelada por substituição', 'status': 'CANCELADA_SUBSTITUICAO', 'pode_utilizar': False},
                '6': {'descricao': 'Cancelada por anulação', 'status': 'CANCELADA_ANULACAO', 'pode_utilizar': False},
                '7': {'descricao': 'Cancelada por determinação judicial', 'status': 'CANCELADA_JUDICIAL', 'pode_utilizar': False},
                '8': {'descricao': 'Cancelada por determinação da administração tributária', 'status': 'CANCELADA_ADMINISTRACAO', 'pode_utilizar': False}
            }
        }
        
        # Verificar se o tipo de documento é suportado
        if tipo_documento not in situacoes:
            tipos_suportados = ', '.join(situacoes.keys())
            return False, f'Tipo de documento não suportado. Tipos suportados: {tipos_suportados}', {}
        
        # Verificar se o código existe no mapeamento para o tipo de documento
        if codigo_limpo not in situacoes[tipo_documento]:
            return False, f'Código de situação inválido para {tipo_documento}', {}
        
        # Obter informações da situação
        info = situacoes[tipo_documento][codigo_limpo]
        
        # Montar objeto de retorno com informações adicionais
        info_retorno = {
            'codigo': codigo_limpo,
            'descricao': info['descricao'],
            'status': info['status'],
            'pode_utilizar': info['pode_utilizar'],
            'tipo_documento': tipo_documento
        }
        
        return True, f'Situação do {tipo_documento}: {info["descricao"]}', info_retorno
    
    def _validar_evento_contingencia(self, codigo: str, tipo_documento: str = 'NFE') -> tuple[bool, str, dict]:
        """
        Valida e retorna informações sobre códigos de eventos e contingência de documentos fiscais
        
        Args:
            codigo: Código do evento/contingência
            tipo_documento: Tipo de documento fiscal ('NFE', 'NFCE', 'CTE', 'MDFE', 'NFSE')
            
        Retorna:
            tuple: (valido: bool, mensagem: str, info: dict)
                info contém:
                - codigo: código do evento
                - descricao: descrição do evento/contingência
                - tipo: tipo de evento (CANCELAMENTO, CARTA_CORRECAO, EPEC, etc.)
                - impacto: impacto do evento (BLOQUEANTE, INFORMATIVO, etc.)
        """
        if not codigo or not str(codigo).strip():
            return False, 'Código de evento/contingência não pode estar vazio', {}
            
        # Remover caracteres não numéricos
        codigo_limpo = ''.join(filter(str.isdigit, str(codigo)))
        tipo_documento = tipo_documento.upper()
        
        # Mapeamento de códigos de eventos/contingência por tipo de documento
        eventos = {
            'NFE': {
                # Eventos de NFe
                '110111': {'descricao': 'Carta de Correção', 'tipo': 'CARTA_CORRECAO', 'impacto': 'INFORMATIVO'},
                '110110': {'descricao': 'Carta de Correção', 'tipo': 'CARTA_CORRECAO', 'impacto': 'INFORMATIVO'},  # Versão anterior
                '110112': {'descricao': 'Cancelamento', 'tipo': 'CANCELAMENTO', 'impacto': 'BLOQUEANTE'},
                '110130': {'descricao': 'Cancelamento por Substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '110131': {'descricao': 'Cancelamento por Substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},  # Versão anterior
                '110140': {'descricao': 'Comprovante de Entrega da NF-e', 'tipo': 'COMPROVANTE_ENTREGA', 'impacto': 'INFORMATIVO'},
                '110150': {'descricao': 'Cancelamento do Comprovante de Entrega da NF-e', 'tipo': 'CANCELAMENTO_COMPROVANTE', 'impacto': 'BLOQUEANTE'},
                '110160': {'descricao': 'Encerramento de MDF-e', 'tipo': 'ENCERRAMENTO_MDFE', 'impacto': 'INFORMATIVO'},
                '110170': {'descricao': 'Confirmação da Operação', 'tipo': 'CONFIRMACAO_OPERACAO', 'impacto': 'INFORMATIVO'},
                '110180': {'descricao': 'Cancelamento de Operação', 'tipo': 'CANCELAMENTO_OPERACAO', 'impacto': 'BLOQUEANTE'},
                '210200': {'descricao': 'Confirmação da Operação', 'tipo': 'CONFIRMACAO_OPERACAO', 'impacto': 'INFORMATIVO'},
                '210210': {'descricao': 'Ciência da Operação', 'tipo': 'CIENCIA_OPERACAO', 'impacto': 'INFORMATIVO'},
                '210220': {'descricao': 'Desconhecimento da Operação', 'tipo': 'DESCONHECIMENTO_OPERACAO', 'impacto': 'INFORMATIVO'},
                '210240': {'descricao': 'Operação não Realizada', 'tipo': 'OPERACAO_NAO_REALIZADA', 'impacto': 'INFORMATIVO'},
                # Contingências
                '110140': {'descricao': 'EPEC - Evento Prévio da Emissão em Contingência', 'tipo': 'EPEC', 'impacto': 'CONTINGENCIA'},
                '111500': {'descricao': 'Pedido de Prorrogação do Prazo de Cancelamento', 'tipo': 'PRORROGACAO_CANCELAMENTO', 'impacto': 'SOLICITACAO'},
                '111501': {'descricao': 'Cancelamento do Pedido de Prorrogação', 'tipo': 'CANCELAMENTO_PRORROGACAO', 'impacto': 'SOLICITACAO'},
            },
            'NFCE': {
                # Mesmos eventos da NFe, com algumas especificidades da NFCe
                '110111': {'descricao': 'Carta de Correção', 'tipo': 'CARTA_CORRECAO', 'impacto': 'INFORMATIVO'},
                '110112': {'descricao': 'Cancelamento', 'tipo': 'CANCELAMENTO', 'impacto': 'BLOQUEANTE'},
                '110130': {'descricao': 'Cancelamento por Substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                # Eventos específicos da NFCe
                '110160': {'descricao': 'Encerramento de MDF-e', 'tipo': 'ENCERRAMENTO_MDFE', 'impacto': 'INFORMATIVO'},
                '110170': {'descricao': 'Confirmação da Operação', 'tipo': 'CONFIRMACAO_OPERACAO', 'impacto': 'INFORMATIVO'},
                '110180': {'descricao': 'Cancelamento de Operação', 'tipo': 'CANCELAMENTO_OPERACAO', 'impacto': 'BLOQUEANTE'},
                # Contingências específicas da NFCe
                '110140': {'descricao': 'EPEC - Evento Prévio da Emissão em Contingência', 'tipo': 'EPEC', 'impacto': 'CONTINGENCIA'},
            },
            'CTE': {
                # Eventos de CT-e
                '110110': {'descricao': 'Carta de Correção', 'tipo': 'CARTA_CORRECAO', 'impacto': 'INFORMATIVO'},
                '110111': {'descricao': 'Cancelamento', 'tipo': 'CANCELAMENTO', 'impacto': 'BLOQUEANTE'},
                '110180': {'descricao': 'Cancelamento de Operação', 'tipo': 'CANCELAMENTO_OPERACAO', 'impacto': 'BLOQUEANTE'},
                '110114': {'descricao': 'Cancelamento de Substituto', 'tipo': 'CANCELAMENTO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110115': {'descricao': 'Cancelamento de Substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '110116': {'descricao': 'Cancelamento de Anulação', 'tipo': 'CANCELAMENTO_ANULACAO', 'impacto': 'BLOQUEANTE'},
                '110117': {'descricao': 'Cancelamento de Anulação de Substituição', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '110118': {'descricao': 'Cancelamento de Anulação de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110119': {'descricao': 'Cancelamento de Anulação de Substituição de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110120': {'descricao': 'Cancelamento de Anulação de Substituição de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                # Contingências de CT-e
                '110140': {'descricao': 'EPEC - Evento Prévio da Emissão em Contingência', 'tipo': 'EPEC', 'impacto': 'CONTINGENCIA'},
            },
            'MDFE': {
                # Eventos de MDF-e
                '110110': {'descricao': 'Carta de Correção', 'tipo': 'CARTA_CORRECAO', 'impacto': 'INFORMATIVO'},
                '110111': {'descricao': 'Cancelamento', 'tipo': 'CANCELAMENTO', 'impacto': 'BLOQUEANTE'},
                '110112': {'descricao': 'Encerramento', 'tipo': 'ENCERRAMENTO', 'impacto': 'BLOQUEANTE'},
                '110114': {'descricao': 'Cancelamento de Substituto', 'tipo': 'CANCELAMENTO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110115': {'descricao': 'Cancelamento de Substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '110116': {'descricao': 'Cancelamento de Anulação', 'tipo': 'CANCELAMENTO_ANULACAO', 'impacto': 'BLOQUEANTE'},
                '110117': {'descricao': 'Cancelamento de Anulação de Substituição', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '110118': {'descricao': 'Cancelamento de Anulação de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110119': {'descricao': 'Cancelamento de Anulação de Substituição de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                '110120': {'descricao': 'Cancelamento de Anulação de Substituição de Substituto', 'tipo': 'CANCELAMENTO_ANULACAO_SUBSTITUICAO_SUBSTITUTO', 'impacto': 'BLOQUEANTE'},
                # Contingências de MDF-e
                '110140': {'descricao': 'EPEC - Evento Prévio da Emissão em Contingência', 'tipo': 'EPEC', 'impacto': 'CONTINGENCIA'},
            },
            'NFSE': {
                # Eventos de NFS-e (varia por município, aqui estão os mais comuns)
                '1': {'descricao': 'Cancelamento', 'tipo': 'CANCELAMENTO', 'impacto': 'BLOQUEANTE'},
                '2': {'descricao': 'Substituição', 'tipo': 'SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
                '3': {'descricao': 'Correção', 'tipo': 'CORRECAO', 'impacto': 'INFORMATIVO'},
                '4': {'descricao': 'Cancelamento por substituição', 'tipo': 'CANCELAMENTO_SUBSTITUICAO', 'impacto': 'BLOQUEANTE'},
            }
        }
        
        # Verificar se o tipo de documento é suportado
        if tipo_documento not in eventos:
            tipos_suportados = ', '.join(eventos.keys())
            return False, f'Tipo de documento não suportado para eventos. Tipos suportados: {tipos_suportados}', {}
        
        # Verificar se o código existe no mapeamento para o tipo de documento
        if codigo_limpo not in eventos[tipo_documento]:
            return False, f'Código de evento/contingência inválido para {tipo_documento}', {}
        
        # Obter informações do evento
        info = eventos[tipo_documento][codigo_limpo]
        
        # Montar objeto de retorno com informações adicionais
        info_retorno = {
            'codigo': codigo_limpo,
            'descricao': info['descricao'],
            'tipo': info['tipo'],
            'impacto': info['impacto'],
            'tipo_documento': tipo_documento
        }
        
        return True, f'Evento/Contingência {tipo_documento}: {info["descricao"]}', info_retorno
    
    def _formatar_cnpj(self, cnpj: str) -> str:
        """
        Formata o CNPJ para exibição
        """
        if not cnpj:
            return ''
            
        # Remover caracteres não numéricos
        cnpj = ''.join(filter(str.isdigit, str(cnpj)))
        
        if len(cnpj) != 14:
            return cnpj  # Retorna sem formatação se não tiver 14 dígitos
            
        return f'{cnpj[:2]}.{cnpj[2:5]}.{cnpj[5:8]}/{cnpj[8:12]}-{cnpj[12:]}'

    def _salvar_conversa(self, usuario_id: int, empresa_id: Optional[int], pergunta: str,
                        resposta: str, contexto_sql: str, dados_utilizados: List[Dict],
                        tempo_resposta: int):
        """
        Salva a conversa no histórico
        """
        try:
            conversa = ChatbotConversas(
                usuario_id=usuario_id,
                empresa_id=empresa_id,
                pergunta=pergunta,
                resposta=resposta,
                contexto_sql=contexto_sql,
                dados_utilizados=dados_utilizados,
                tempo_resposta=tempo_resposta
            )

            db.session.add(conversa)
            db.session.commit()

        except Exception as e:
            print(f"Erro ao salvar conversa: {e}")
            db.session.rollback()

    def obter_historico(self, usuario_id: int, limite: int = 20) -> List[Dict]:
        """
        Obtém o histórico de conversas do usuário
        """
        try:
            conversas = ChatbotConversas.query.filter_by(usuario_id=usuario_id)\
                                             .order_by(ChatbotConversas.data_criacao.desc())\
                                             .limit(limite).all()

            return [conversa.to_dict() for conversa in conversas]
        except Exception as e:
            print(f"Erro ao obter histórico: {e}")
            return []

    def avaliar_resposta(self, conversa_id: int, avaliacao: int, feedback: Optional[str] = None):
        """
        Permite ao usuário avaliar uma resposta do chatbot
        """
        try:
            conversa = ChatbotConversas.query.get(conversa_id)
            if conversa:
                conversa.avaliacao = avaliacao
                conversa.feedback = feedback
                db.session.commit()
                return True
        except Exception as e:
            print(f"Erro ao avaliar resposta: {e}")

        return False
