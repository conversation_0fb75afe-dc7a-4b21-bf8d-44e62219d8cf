/**
 * auditoria_fiscal.js - Funções para auditoria fiscal
 */

console.log('Carregando auditoria_fiscal.js');

/**
 * Executa a auditoria fiscal para todos os cenários de um tipo de tributo
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 * @param {string} direcao - Direção (entrada ou saida)
 */
function executarAuditoriaTributo(tipoTributo, direcao) {
  // Mostrar loading
  showLoading();

  // Obter empresa selecionada
  const empresaId = localStorage.getItem('selectedCompany');
  if (!empresaId) {
    hideLoading();
    showErrorMessage('Selecione uma empresa para executar a auditoria.');
    return;
  }

  // Sempre forçar o recálculo para garantir valores corretos
  const forcarRecalculo = true;

  // Dados para a requisição
  const data = {
    tipo_tributo: tipoTributo,
    direcao: direcao,
    empresa_id: empresaId,
    forcar_recalculo: forcarRecalculo,
  };

  // Executar a auditoria
  console.log('Executando auditoria para:', data);

  // Usar a URL completa com a porta correta
  fetch(`http://127.0.0.1:5000/api/auditoria/tributos`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      console.log('Status da resposta:', response.status, response.statusText);

      // Verificar se a resposta é um erro HTTP
      if (!response.ok) {
        if (response.status === 405) {
          throw new Error(
            'Método não permitido. Verifique se a rota suporta requisições POST.',
          );
        }
        return response.text().then((text) => {
          try {
            // Tentar converter para JSON
            return JSON.parse(text);
          } catch (e) {
            // Se não for JSON, retornar o texto como erro
            throw new Error(`Erro HTTP ${response.status}: ${text}`);
          }
        });
      }

      return response.json();
    })
    .then((data) => {
      // Esconder loading
      hideLoading();

      console.log('Resposta da auditoria:', data);

      if (data.success && data.audit_id) {
        // Iniciar acompanhamento de progresso via WebSocket
        if (window.auditoriaProgressManager) {
          window.auditoriaProgressManager.startAuditProgress(
            data.audit_id,
            data.total_tributos || 0,
          );
        } else {
          console.warn('Gerenciador de progresso da auditoria não encontrado');
          showSuccessMessage('Auditoria iniciada com sucesso!');
        }
      } else {
        // Mostrar mensagem de erro
        const errorMsg = data.message || 'Erro ao executar auditoria';
        console.error('Erro retornado pelo servidor:', errorMsg);
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro detalhada
      console.error('Erro ao executar auditoria:', error);
      showErrorMessage(`Erro ao executar auditoria: ${error.message}`);
    });
}

function executarAuditoriaPorCenario(cenarioId, tipoTributo) {
  // Mostrar loading
  showLoading();

  // Perguntar se deseja forçar o recálculo
  const forcarRecalculo = confirm(
    'Deseja forçar o recálculo de tributos já auditados?',
  );

  // Dados para a requisição
  const data = {
    tipo_tributo: tipoTributo,
    forcar_recalculo: forcarRecalculo,
  };

  // Executar a auditoria
  console.log(
    'Executando auditoria para cenário:',
    cenarioId,
    'tipo:',
    tipoTributo,
  );

  // Usar a URL completa com a porta correta
  fetch(`http://127.0.0.1:5000/api/auditoria/cenarios/${cenarioId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      console.log('Status da resposta:', response.status, response.statusText);

      // Verificar se a resposta é um erro HTTP
      if (!response.ok) {
        if (response.status === 405) {
          throw new Error(
            'Método não permitido. Verifique se a rota suporta requisições POST.',
          );
        }
        return response.text().then((text) => {
          try {
            // Tentar converter para JSON
            return JSON.parse(text);
          } catch (e) {
            // Se não for JSON, retornar o texto como erro
            throw new Error(`Erro HTTP ${response.status}: ${text}`);
          }
        });
      }

      return response.json();
    })
    .then((data) => {
      // Esconder loading
      hideLoading();

      console.log('Resposta da auditoria por cenário:', data);

      if (data.success) {
        // Mostrar mensagem de sucesso
        showSuccessMessage('Auditoria executada com sucesso!');

        // Mostrar resultados
        const total = data.total || 0;
        const auditados = data.auditados || 0;
        const naoAuditados = data.nao_auditados || 0;

        const tipoTributoUpper = tipoTributo.toUpperCase();
        const porTipo =
          data.por_tipo && data.por_tipo[tipoTributo]
            ? data.por_tipo[tipoTributo]
            : { auditados: 0, nao_auditados: 0 };

        const message = `
          <div class="alert alert-info">
            <h5>Resultado da Auditoria</h5>
            <p>Total de tributos: ${total}</p>
            <p>Tributos auditados: ${auditados}</p>
            <p>Tributos não auditados: ${naoAuditados}</p>
            <hr>
            <p>${tipoTributoUpper} auditados: ${porTipo.auditados}</p>
            <p>${tipoTributoUpper} não auditados: ${porTipo.nao_auditados}</p>
          </div>
        `;

        // Mostrar modal com resultados
        showModal('Resultado da Auditoria', message);

        // Atualizar a tabela de cenários
        if (typeof loadCenarioTable === 'function') {
          loadCenarioTable();
        }
      } else {
        // Mostrar mensagem de erro
        const errorMsg = data.message || 'Erro ao executar auditoria';
        console.error('Erro retornado pelo servidor:', errorMsg);
        showErrorMessage(errorMsg);
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro detalhada
      console.error('Erro ao executar auditoria por cenário:', error);
      showErrorMessage(`Erro ao executar auditoria: ${error.message}`);
    });
}

/**
 * Adiciona o botão de auditoria na página de detalhes de cenário
 * @param {number} cenarioId - ID do cenário
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 */

/**
 * Carrega a lista de tributos auditados
 * @param {Object} filters - Filtros para a consulta
 */

/**
 * Renderiza a tabela de tributos auditados
 * @param {Array} tributos - Lista de tributos auditados
 */
function renderizarTabelaTributos(tributos) {
  // Obter o container da tabela
  const tableContainer = document.getElementById('tributos-table-container');
  if (!tableContainer) {
    console.error('Container da tabela não encontrado');
    return;
  }

  // Limpar o container
  tableContainer.innerHTML = '';

  // Verificar se há tributos
  if (!tributos || tributos.length === 0) {
    tableContainer.innerHTML =
      '<div class="alert alert-info">Nenhum tributo auditado encontrado</div>';
    return;
  }

  // Criar a tabela
  const table = document.createElement('table');
  table.className = 'table table-striped table-bordered';
  table.id = 'tributos-table';

  // Criar o cabeçalho da tabela
  const thead = document.createElement('thead');
  thead.innerHTML = `
    <tr>
      <th>ID</th>
      <th>Cliente</th>
      <th>Produto</th>
      <th>Data Emissão</th>
      <th>Valor Total</th>
      <th>Status Auditoria</th>
      <th>Data Auditoria</th>
      <th>Ações</th>
    </tr>
  `;
  table.appendChild(thead);

  // Criar o corpo da tabela
  const tbody = document.createElement('tbody');
  tributos.forEach((tributo) => {
    const tr = document.createElement('tr');
    tr.innerHTML = `
      <td>${tributo.id}</td>
      <td>${tributo.cliente ? tributo.cliente.razao_social : '-'}</td>
      <td>${tributo.produto ? tributo.produto.descricao : '-'}</td>
      <td>${formatDate(tributo.data_emissao)}</td>
      <td>${formatCurrency(tributo.valor_total)}</td>
      <td>
        ${getAuditoriaStatusByTipo(tributo, filters && filters.tipo_tributo)}
      </td>
      <td>
        ${getAuditoriaDataByTipo(tributo, filters && filters.tipo_tributo)}
      </td>
      <td>
        <button class="btn btn-sm btn-info" onclick="visualizarTributo(${
          tributo.id
        })">
          <i class="fas fa-eye"></i>
        </button>
      </td>
    `;
    tbody.appendChild(tr);
  });
  table.appendChild(tbody);

  // Adicionar a tabela ao container
  tableContainer.appendChild(table);

  // Inicializar DataTable
  $(table).DataTable({
    responsive: true,
    language: {
      url: '//cdn.datatables.net/plug-ins/1.10.25/i18n/Portuguese-Brasil.json',
    },
  });
}

/**
 * Visualiza os detalhes de um tributo auditado
 * @param {number} tributoId - ID do tributo
 */
function visualizarTributo(tributoId) {
  // Mostrar loading
  showLoading();

  // Buscar detalhes do tributo
  fetch(`http://127.0.0.1:5000/api/auditoria/tributos/${tributoId}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${getToken()}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      // Esconder loading
      hideLoading();

      if (data.success) {
        // Mostrar modal com detalhes do tributo
        mostrarModalDetalhesTributo(data.tributo);
      } else {
        // Mostrar mensagem de erro
        showErrorMessage(
          data.message || 'Erro ao carregar detalhes do tributo',
        );
      }
    })
    .catch((error) => {
      // Esconder loading
      hideLoading();

      // Mostrar mensagem de erro
      console.error('Erro ao carregar detalhes do tributo:', error);
      showErrorMessage('Erro ao carregar detalhes do tributo');
    });
}

/**
 * Retorna o status de auditoria específico para o tipo de tributo
 * @param {Object} tributo - Dados do tributo
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 * @returns {string} Status de auditoria
 */
function getAuditoriaStatusByTipo(tributo, tipoTributo) {
  if (!tributo) return 'pendente';

  if (tipoTributo === 'icms' && tributo.auditoria_icms_status) {
    return tributo.auditoria_icms_status;
  } else if (tipoTributo === 'icms_st' && tributo.auditoria_icms_st_status) {
    return tributo.auditoria_icms_st_status;
  } else if (tipoTributo === 'ipi' && tributo.auditoria_ipi_status) {
    return tributo.auditoria_ipi_status;
  } else if (tipoTributo === 'pis' && tributo.auditoria_pis_status) {
    return tributo.auditoria_pis_status;
  } else if (tipoTributo === 'cofins' && tributo.auditoria_cofins_status) {
    return tributo.auditoria_cofins_status;
  } else if (tipoTributo === 'difal' && tributo.auditoria_difal_status) {
    return tributo.auditoria_difal_status;
  }

  // Se não especificar tipo ou não encontrar status específico, retorna o status geral
  return tributo.auditoria_status || 'pendente';
}

/**
 * Retorna a data de auditoria específica para o tipo de tributo
 * @param {Object} tributo - Dados do tributo
 * @param {string} tipoTributo - Tipo de tributo (icms, icms_st, ipi, pis, cofins, difal)
 * @returns {string} Data de auditoria formatada
 */
function getAuditoriaDataByTipo(tributo, tipoTributo) {
  if (!tributo) return '-';

  if (tipoTributo === 'icms' && tributo.auditoria_icms_data) {
    return formatDate(tributo.auditoria_icms_data);
  } else if (tipoTributo === 'icms_st' && tributo.auditoria_icms_st_data) {
    return formatDate(tributo.auditoria_icms_st_data);
  } else if (tipoTributo === 'ipi' && tributo.auditoria_ipi_data) {
    return formatDate(tributo.auditoria_ipi_data);
  } else if (tipoTributo === 'pis' && tributo.auditoria_pis_data) {
    return formatDate(tributo.auditoria_pis_data);
  } else if (tipoTributo === 'cofins' && tributo.auditoria_cofins_data) {
    return formatDate(tributo.auditoria_cofins_data);
  } else if (tipoTributo === 'difal' && tributo.auditoria_difal_data) {
    return formatDate(tributo.auditoria_difal_data);
  }

  // Se não especificar tipo ou não encontrar data específica, retorna a data geral
  return formatDate(tributo.auditoria_data);
}

/**
 * Mostra o modal com os detalhes do tributo auditado
 * @param {Object} tributo - Dados do tributo
 */
function mostrarModalDetalhesTributo(tributo) {
  // Criar o conteúdo do modal
  let content = `
    <div class="row">
      <div class="col-md-6">
        <h5>Informações Gerais</h5>
        <p><strong>Cliente:</strong> ${
          tributo.cliente ? tributo.cliente.razao_social : '-'
        }</p>
        <p><strong>Produto:</strong> ${
          tributo.produto ? tributo.produto.descricao : '-'
        }</p>
        <p><strong>Data Emissão:</strong> ${formatDate(
          tributo.data_emissao,
        )}</p>
        <p><strong>Data Saída:</strong> ${formatDate(tributo.data_saida)}</p>
        <p><strong>Valor Total:</strong> ${formatCurrency(
          tributo.valor_total,
        )}</p>
        <p><strong>Status Auditoria Geral:</strong> ${
          tributo.auditoria_status || 'pendente'
        }</p>
        <p><strong>Data Auditoria Geral:</strong> ${formatDate(
          tributo.auditoria_data,
        )}</p>
        <hr>
        <p><strong>Status Auditoria ICMS:</strong> ${
          tributo.auditoria_icms_status || 'pendente'
        }</p>
        <p><strong>Status Auditoria ICMS-ST:</strong> ${
          tributo.auditoria_icms_st_status || 'pendente'
        }</p>
        <p><strong>Status Auditoria IPI:</strong> ${
          tributo.auditoria_ipi_status || 'pendente'
        }</p>
        <p><strong>Status Auditoria PIS:</strong> ${
          tributo.auditoria_pis_status || 'pendente'
        }</p>
        <p><strong>Status Auditoria COFINS:</strong> ${
          tributo.auditoria_cofins_status || 'pendente'
        }</p>
        <p><strong>Status Auditoria DIFAL:</strong> ${
          tributo.auditoria_difal_status || 'pendente'
        }</p>
      </div>
      <div class="col-md-6">
        <h5>Valores Importados vs. Auditados</h5>
        <table class="table table-sm">
          <thead>
            <tr>
              <th>Tributo</th>
              <th>Base Cálculo</th>
              <th>Valor</th>
              <th>Base Cálculo Auditada</th>
              <th>Valor Auditado</th>
            </tr>
          </thead>
          <tbody>
  `;
  // ICMS
  content += `
    <tr>
      <td>ICMS</td>
      <td>${formatCurrency(tributo.icms_vbc)}</td>
      <td>${formatCurrency(tributo.icms_valor)}</td>
      <td>${formatCurrency(tributo.cenario_icms_vbc)}</td>
      <td>${formatCurrency(tributo.cenario_icms_valor)}</td>
    </tr>
  `;

  // ICMS-ST
  content += `
    <tr>
      <td>ICMS-ST</td>
      <td>${formatCurrency(tributo.icms_st_vbc)}</td>
      <td>${formatCurrency(tributo.icms_st_valor)}</td>
      <td>${formatCurrency(tributo.cenario_icms_st_vbc)}</td>
      <td>${formatCurrency(tributo.cenario_icms_st_valor)}</td>
    </tr>
  `;

  // IPI
  content += `
    <tr>
      <td>IPI</td>
      <td>${formatCurrency(tributo.ipi_vbc)}</td>
      <td>${formatCurrency(tributo.ipi_valor)}</td>
      <td>${formatCurrency(tributo.cenario_ipi_vbc)}</td>
      <td>${formatCurrency(tributo.cenario_ipi_valor)}</td>
    </tr>
  `;

  // PIS
  content += `
    <tr>
      <td>PIS</td>
      <td>${formatCurrency(tributo.pis_vbc)}</td>
      <td>${formatCurrency(tributo.pis_valor)}</td>
      <td>${formatCurrency(tributo.cenario_pis_vbc)}</td>
      <td>${formatCurrency(tributo.cenario_pis_valor)}</td>
    </tr>
  `;

  // COFINS
  content += `
    <tr>
      <td>COFINS</td>
      <td>${formatCurrency(tributo.cofins_vbc)}</td>
      <td>${formatCurrency(tributo.cofins_valor)}</td>
      <td>${formatCurrency(tributo.cenario_cofins_vbc)}</td>
      <td>${formatCurrency(tributo.cenario_cofins_valor)}</td>
    </tr>
  `;

  // Fechar tabela e divs
  content += `
          </tbody>
        </table>
      </div>
    </div>
  `;

  // Mostrar modal
  showModal(`Detalhes do Tributo #${tributo.id}`, content, 'lg');
}

// Funções auxiliares

/**
 * Formata um valor monetário
 * @param {number} value - Valor a ser formatado
 * @returns {string} - Valor formatado
 */
function formatCurrency(value) {
  if (value === null || value === undefined) return '-';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Formata uma data
 * @param {string} dateString - Data em formato ISO
 * @returns {string} - Data formatada
 */
function formatDate(dateString) {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('pt-BR');
}

/**
 * Obtém o token de autenticação
 * @returns {string} Token de autenticação
 */
function getToken() {
  return localStorage.getItem('token');
}

/**
 * Obtém o valor de um parâmetro da URL
 * @param {string} name - Nome do parâmetro
 * @returns {string} Valor do parâmetro
 */
function getUrlParameter(name) {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get(name);
}

/**
 * Mostra um indicador de carregamento
 */
function showLoading() {
  // Verificar se já existe um loading
  if (document.getElementById('loading-overlay')) {
    return;
  }

  // Criar o overlay de loading
  const loadingOverlay = document.createElement('div');
  loadingOverlay.id = 'loading-overlay';
  loadingOverlay.className = 'loading-overlay';
  loadingOverlay.innerHTML = `
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Carregando...</span>
    </div>
  `;

  // Adicionar o overlay ao body
  document.body.appendChild(loadingOverlay);
}

/**
 * Esconde o indicador de carregamento
 */
function hideLoading() {
  const loadingOverlay = document.getElementById('loading-overlay');
  if (loadingOverlay) {
    loadingOverlay.remove();
  }
}

/**
 * Mostra uma mensagem de sucesso
 * @param {string} message - Mensagem a ser exibida
 */
function showSuccessMessage(message) {
  alert(message);
}

/**
 * Mostra uma mensagem de erro
 * @param {string} message - Mensagem a ser exibida
 */
function showErrorMessage(message) {
  alert(message);
}

/**
 * Mostra um modal com conteúdo personalizado
 * @param {string} title - Título do modal
 * @param {string} content - Conteúdo do modal
 * @param {string} size - Tamanho do modal (sm, md, lg, xl)
 */
function showModal(title, content, size = 'md') {
  // Verificar se já existe um modal
  let modal = document.getElementById('dynamic-modal');
  if (!modal) {
    // Criar o modal
    modal = document.createElement('div');
    modal.id = 'dynamic-modal';
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.setAttribute('aria-hidden', 'true');

    // Adicionar o modal ao body
    document.body.appendChild(modal);
  }

  // Definir o conteúdo do modal
  modal.innerHTML = `
    <div class="modal-dialog modal-${size}">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">${title}</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
        </div>
        <div class="modal-body">
          ${content}
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
        </div>
      </div>
    </div>
  `;

  // Inicializar e mostrar o modal
  const modalInstance = new bootstrap.Modal(modal);
  modalInstance.show();
}

// Inicialização
document.addEventListener('DOMContentLoaded', function () {
  console.log('DOM carregado - auditoria_fiscal.js');

  // Verificar se estamos na página de auditoria
  if (document.querySelector('.auditoria-page')) {
    console.log('Detectada página de auditoria');

    // Inicializar filtros
    if (typeof initFiltrosAuditoria === 'function') {
      initFiltrosAuditoria();
    }

    // Carregar tributos auditados (função comentada temporariamente)
    const empresaId = localStorage.getItem('selectedCompany');
    if (empresaId && typeof carregarTributosAuditados === 'function') {
      carregarTributosAuditados({ empresa_id: empresaId });
    }
  }
});
