# Import models to make them available when importing from models package
from .escritorio import Escritorio, db
from .empresa import Empresa
from .usuario import Usuario
from .cliente import Cliente
from .produto import Produto
from .tributo import Tributo
from .importacao_xml import ImportacaoXML
from .tributo_historico import TributoHistorico
from .cenario import CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL
from .nota_fiscal_item import NotaFiscalItem
from .auditoria_resultado import AuditoriaResultado
from .auditoria_sumario import AuditoriaSumario
from .chatbot_conversas import ChatbotConversas
from .chatbot_templates import ChatbotTemplates

# Models para notas de entrada
from .cliente_entrada import ClienteEntrada
from .produto_entrada import ProdutoEntrada
from .nota_entrada import NotaEntrada
from .item_nota_entrada import ItemNotaEntrada
from .importacao_sped import ImportacaoSped
