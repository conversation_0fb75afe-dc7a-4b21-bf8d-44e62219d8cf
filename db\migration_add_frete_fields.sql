-- Migration para adicionar campos de frete
-- Data: 2024-12-19
-- Descrição: Adiciona campos para processar frete nos tributos e cenários

-- Adicionar campo valor_frete na tabela tributo
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS valor_frete DECIMAL(10, 2);

-- Adici<PERSON>r campo incluir_frete nas tabelas de cenários
ALTER TABLE cenario_icms ADD COLUMN IF NOT EXISTS incluir_frete BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_icms_st ADD COLUMN IF NOT EXISTS incluir_frete BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_ipi ADD COLUMN IF NOT EXISTS incluir_frete BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_pis ADD COLUMN IF NOT EXISTS incluir_frete BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_cofins ADD COLUMN IF NOT EXISTS incluir_frete BOOLEAN DEFAULT TRUE;

-- Comentários para documentação
COMMENT ON COLUMN tributo.valor_frete IS 'Valor do frete proporcional ao item da nota fiscal';
COMMENT ON COLUMN cenario_icms.incluir_frete IS 'Define se o frete deve ser incluído na base de cálculo do ICMS';
COMMENT ON COLUMN cenario_icms_st.incluir_frete IS 'Define se o frete deve ser incluído na base de cálculo do ICMS-ST';
COMMENT ON COLUMN cenario_ipi.incluir_frete IS 'Define se o frete deve ser incluído na base de cálculo do IPI';
COMMENT ON COLUMN cenario_pis.incluir_frete IS 'Define se o frete deve ser incluído na base de cálculo do PIS';
COMMENT ON COLUMN cenario_cofins.incluir_frete IS 'Define se o frete deve ser incluído na base de cálculo do COFINS';

-- Atualizar registros existentes para incluir frete por padrão
UPDATE cenario_icms SET incluir_frete = TRUE WHERE incluir_frete IS NULL;
UPDATE cenario_icms_st SET incluir_frete = TRUE WHERE incluir_frete IS NULL;
UPDATE cenario_ipi SET incluir_frete = TRUE WHERE incluir_frete IS NULL;
UPDATE cenario_pis SET incluir_frete = TRUE WHERE incluir_frete IS NULL;
UPDATE cenario_cofins SET incluir_frete = TRUE WHERE incluir_frete IS NULL;
