-- Migration to add cenario_status column to auditoria_resultado table
-- Data: 2024-12-19
-- Descrição: Adiciona coluna para armazenar o status do cenário utilizado no cálculo da auditoria

-- Adicionar coluna para status do cenário utilizado
ALTER TABLE auditoria_resultado 
ADD COLUMN IF NOT EXISTS cenario_status VARCHAR(20) DEFAULT 'producao';

-- Comentário da coluna
COMMENT ON COLUMN auditoria_resultado.cenario_status IS 'Status do cenário utilizado no cálculo: producao ou inconsistente';

-- Atualizar registros existentes para ter status 'producao' (padrão para cenários já calculados)
UPDATE auditoria_resultado 
SET cenario_status = 'producao' 
WHERE cenario_status IS NULL;

-- Criar índice para melhorar performance das consultas
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_cenario_status 
ON auditoria_resultado(cenario_status);

-- Verificar se a migração foi aplicada corretamente
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'auditoria_resultado' 
AND column_name = 'cenario_status';
