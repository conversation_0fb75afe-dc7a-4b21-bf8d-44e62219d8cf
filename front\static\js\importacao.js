/**
 * importacao.js - Auditoria Fiscal
 * Funções para gerenciar a importação de arquivos XML
 */

document.addEventListener('DOMContentLoaded', function () {
  // Verificar se estamos na página de importação
  if (window.location.pathname === '/importacao') {
    setupImportacaoPage();
  }
});

/**
 * Configura a página de importação
 */
function setupImportacaoPage() {
  // Carregar o conteúdo da página
  loadImportacaoContent();
}

/**
 * Carrega o conteúdo da página de importação
 */
function loadImportacaoContent() {
  const pageContent = document.getElementById('page-content');
  if (!pageContent) return;

  // Verificar se a seção já existe
  let importacaoSection = document.getElementById('page-importacao');
  if (importacaoSection) {
    importacaoSection.classList.add('active');
    loadImportacaoData();
    return;
  }

  // Criar a seção de importação
  importacaoSection = document.createElement('div');
  importacaoSection.id = 'page-importacao';
  importacaoSection.className = 'page-section active';

  // Conteúdo HTML da página
  importacaoSection.innerHTML = `
    <div class="section-header">
      <h2><i class="fas fa-file-import"></i> Importação de Arquivos</h2>
    </div>

    <!-- Abas para XML e SPED -->
    <ul class="nav nav-tabs mb-4" id="import-tabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="xml-tab" data-bs-toggle="tab" data-bs-target="#xml-panel" type="button" role="tab">
          <i class="fas fa-file-code"></i> Importação XML
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="sped-tab" data-bs-toggle="tab" data-bs-target="#sped-panel" type="button" role="tab">
          <i class="fas fa-file-alt"></i> Importação SPED
        </button>
      </li>
    </ul>

    <div class="tab-content" id="import-tab-content">
      <!-- Painel XML -->
      <div class="tab-pane fade show active" id="xml-panel" role="tabpanel">
        <div class="card mb-4">
          <div class="card-body">
            <h5 class="card-title">Importar Arquivo XML</h5>
            <p class="card-text">Selecione um arquivo XML para importar os dados fiscais.</p>

            <form id="import-xml-form" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="xml-file" class="form-label">Arquivos XML</label>
                <input type="file" class="form-control" id="xml-file" accept=".xml" multiple required>
                <div class="form-text">
                  O sistema identificará automaticamente as empresas pelos CNPJs dos emitentes nos XMLs.
                  Certifique-se de que as empresas já estão cadastradas no sistema.
                  Você pode selecionar múltiplos arquivos para importação em lote.
                </div>
              </div>

              <div class="btn-group" role="group">
                <button type="submit" class="btn btn-primary" data-mode="single">
                  <i class="fas fa-upload"></i> Importar Individual
                </button>
                <button type="submit" class="btn btn-success" data-mode="batch">
                  <i class="fas fa-cloud-upload-alt"></i> Importar em Lote
                </button>
              </div>
            </form>

            <div id="import-xml-result" class="mt-3 d-none"></div>
          </div>
        </div>
      </div>

      <!-- Painel SPED -->
      <div class="tab-pane fade" id="sped-panel" role="tabpanel">
        <div class="card mb-4">
          <div class="card-body">
            <h5 class="card-title">Importar Arquivo SPED</h5>
            <p class="card-text">Selecione um arquivo SPED (.txt) para importar as notas de entrada.</p>

            <form id="import-sped-form" enctype="multipart/form-data">
              <div class="mb-3">
                <label for="sped-file" class="form-label">Arquivo SPED</label>
                <input type="file" class="form-control" id="sped-file" accept=".txt" required>
                <div class="form-text">
                  O sistema identificará automaticamente a empresa pelo CNPJ no arquivo SPED.
                  Certifique-se de que a empresa já está cadastrada no sistema.
                  Apenas notas de entrada (IND_OPER = 0) serão importadas.
                </div>
              </div>

              <button type="submit" class="btn btn-primary">
                <i class="fas fa-upload"></i> Importar SPED
              </button>
            </form>

            <div id="import-sped-result" class="mt-3 d-none"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Histórico de Importações</h5>

        <!-- Abas para histórico -->
        <ul class="nav nav-pills mb-3" id="history-tabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="xml-history-tab" data-bs-toggle="pill" data-bs-target="#xml-history" type="button" role="tab">
              XML
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="sped-history-tab" data-bs-toggle="pill" data-bs-target="#sped-history" type="button" role="tab">
              SPED
            </button>
          </li>
        </ul>

        <div class="tab-content" id="history-tab-content">
          <div class="tab-pane fade show active" id="xml-history" role="tabpanel">
            <div id="importacoes-xml-content">
              <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </div>
            </div>
          </div>
          <div class="tab-pane fade" id="sped-history" role="tabpanel">
            <div id="importacoes-sped-content">
              <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Adicionar a seção ao conteúdo da página
  pageContent.appendChild(importacaoSection);

  // Configurar os formulários de importação
  setupImportForms();

  // Carregar dados de importações
  loadImportacaoData();
}

/**
 * Configura os formulários de importação
 */
function setupImportForms() {
  // Configurar formulário XML
  const xmlForm = document.getElementById('import-xml-form');
  if (xmlForm) {
    xmlForm.addEventListener('submit', function (event) {
      event.preventDefault();
      const button = event.submitter;
      const mode = button.getAttribute('data-mode') || 'single';
      importarXML(mode);
    });
  }

  // Configurar formulário SPED
  const spedForm = document.getElementById('import-sped-form');
  if (spedForm) {
    spedForm.addEventListener('submit', function (event) {
      event.preventDefault();
      importarSPED();
    });
  }

  // Configurar abas de histórico
  const historyTabs = document.querySelectorAll(
    '#history-tabs button[data-bs-toggle="pill"]',
  );
  historyTabs.forEach((tab) => {
    tab.addEventListener('shown.bs.tab', function (event) {
      const target = event.target.getAttribute('data-bs-target');
      if (target === '#sped-history') {
        loadImportacaoSpedData();
      } else if (target === '#xml-history') {
        loadImportacaoXmlData();
      }
    });
  });
}

// Função loadEmpresasForSelect removida pois não é mais necessária

/**
 * Importa um arquivo XML
 */
function importarXML(mode = 'single') {
  const form = document.getElementById('import-xml-form');
  const resultDiv = document.getElementById('import-xml-result');
  const fileInput = document.getElementById('xml-file');

  if (!form || !resultDiv || !fileInput) return;

  // Verificar se arquivos foram selecionados
  if (!fileInput.files || fileInput.files.length === 0) {
    showImportResult('error', 'Selecione pelo menos um arquivo XML');
    return;
  }

  // Verificar modo de importação
  if (mode === 'single' && fileInput.files.length > 1) {
    showImportResult(
      'error',
      'Selecione apenas um arquivo para importação individual',
    );
    return;
  }

  // Criar FormData para envio do(s) arquivo(s)
  const formData = new FormData();

  if (mode === 'single') {
    formData.append('arquivo', fileInput.files[0]);
  } else {
    // Para importação em lote, adicionar todos os arquivos
    Array.from(fileInput.files).forEach((file) => {
      formData.append('arquivos', file);
    });
  }

  // Mostrar indicador de carregamento
  const message =
    mode === 'single'
      ? 'Importando arquivo...'
      : `Preparando importação de ${fileInput.files.length} arquivos...`;
  showImportResult('loading', message);

  // Para importação em lote, configurar WebSocket
  let socket = null;
  if (mode === 'batch') {
    socket = setupWebSocket();
  }

  // Enviar o(s) arquivo(s) para a API
  const endpoint =
    mode === 'single' ? '/api/importacoes' : '/api/importacoes/batch';

  fetch(endpoint, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success || data.importacao || data.import_id || data.results) {
        if (mode === 'batch' && data.import_id) {
          // Importação em lote - configurar WebSocket para acompanhar progresso
          if (socket) {
            setupImportProgress(socket, data.import_id, data.total_files);
          } else {
            // Fallback se WebSocket não estiver disponível
            showImportResult(
              'info',
              'Importação iniciada. Aguarde a conclusão...',
            );
            // Polling como fallback (opcional)
            // pollImportStatus(data.import_id);
          }
        } else if (data.results) {
          // Resposta de importação em lote já concluída (fallback)
          const total = data.results.total;
          const success = data.results.success.length;
          const errors = data.results.errors.length;
          const invalid = data.invalid_files ? data.invalid_files.length : 0;

          let message = `Importação em lote concluída:<br>`;
          message += `- ${success} arquivo(s) importado(s) com sucesso<br>`;
          if (errors > 0) message += `- ${errors} arquivo(s) com erro<br>`;
          if (invalid > 0) message += `- ${invalid} arquivo(s) inválido(s)<br>`;

          showImportResult(errors === 0 ? 'success' : 'warning', message);
          loadImportacaoData();
          form.reset();
        } else {
          // Importação individual
          showImportResult(
            'success',
            'XML importado com sucesso',
            'import-xml-result',
          );
          loadImportacaoXmlData();
          form.reset();
        }
      } else {
        showImportResult(
          'error',
          data.message || 'Erro ao importar XML',
          'import-xml-result',
        );
      }
    })
    .catch((error) => {
      console.error('Erro ao importar XML:', error);
      showImportResult('error', 'Erro ao importar XML', 'import-xml-result');
    });
}

/**
 * Exibe o resultado da importação
 */
function showImportResult(type, message, elementId = 'import-result') {
  const resultDiv = document.getElementById(elementId);
  if (!resultDiv) return;

  // Limpar classes anteriores
  resultDiv.className = 'mt-3';

  // Configurar aparência com base no tipo
  if (type === 'loading') {
    resultDiv.classList.add('alert', 'alert-info');
    resultDiv.innerHTML = `
      <div class="d-flex align-items-center">
        <div class="spinner-border spinner-border-sm me-2" role="status">
          <span class="visually-hidden">Carregando...</span>
        </div>
        <span>${message}</span>
      </div>
    `;
  } else if (type === 'success') {
    resultDiv.classList.add('alert', 'alert-success');
    resultDiv.innerHTML = `<i class="fas fa-check-circle me-2"></i> ${message}`;
  } else if (type === 'warning') {
    resultDiv.classList.add('alert', 'alert-warning');
    resultDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> ${message}`;
  } else if (type === 'error') {
    resultDiv.classList.add('alert', 'alert-danger');
    resultDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i> ${message}`;
  }
}

/**
 * Carrega os dados de importações (função principal)
 */
function loadImportacaoData() {
  // Carregar dados XML por padrão
  loadImportacaoXmlData();
}

/**
 * Configura WebSocket para acompanhar progresso da importação
 */
function setupWebSocket() {
  try {
    // Verificar se Socket.IO está disponível
    if (typeof io === 'undefined') {
      console.warn('Socket.IO não está disponível');
      return null;
    }

    const socket = io();

    socket.on('connect', () => {
      console.log('WebSocket conectado');
    });

    socket.on('disconnect', () => {
      console.log('WebSocket desconectado');
    });

    socket.on('connect_error', (error) => {
      console.error('Erro de conexão WebSocket:', error);
    });

    return socket;
  } catch (error) {
    console.error('Erro ao configurar WebSocket:', error);
    return null;
  }
}

/**
 * Configura o acompanhamento de progresso da importação
 */
function setupImportProgress(socket, importId, totalFiles) {
  if (!socket || !importId) {
    console.warn('Socket ou importId não fornecidos');
    return;
  }

  // Aguardar um pouco para garantir que a conexão WebSocket esteja estabelecida
  setTimeout(() => {
    // Entrar na sala da importação
    socket.emit('join_import', {
      token: localStorage.getItem('token'),
      import_id: importId,
    });

    console.log(`Entrando na sala de importação: ${importId}`);
  }, 100);

  // Configurar barra de progresso
  showProgressBar(0, totalFiles);

  // Escutar atualizações de progresso
  socket.on('import_progress', (data) => {
    updateProgressBar(
      data.processed,
      data.total,
      data.current_file,
      data.success_count,
      data.error_count,
    );
  });

  // Escutar conclusão da importação
  socket.on('import_complete', (data) => {
    handleImportComplete(data);
    socket.disconnect();
  });

  // Escutar erros da importação
  socket.on('import_error', (data) => {
    handleImportError(data);
    socket.disconnect();
  });
}

/**
 * Mostra a barra de progresso
 */
function showProgressBar(processed, total) {
  const resultDiv = document.getElementById('import-result');
  if (!resultDiv) return;

  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  resultDiv.className = 'mt-3 alert alert-info';
  resultDiv.innerHTML = `
    <div class="d-flex flex-column">
      <div class="d-flex justify-content-between align-items-center mb-2">
        <span><strong>Importando arquivos...</strong></span>
        <span class="badge bg-primary">${processed}/${total}</span>
      </div>
      <div class="progress mb-2" style="height: 20px;">
        <div class="progress-bar progress-bar-striped progress-bar-animated"
             role="progressbar"
             style="width: ${percentage}%"
             aria-valuenow="${percentage}"
             aria-valuemin="0"
             aria-valuemax="100">
          ${percentage}%
        </div>
      </div>
      <div class="d-flex justify-content-between">
        <small class="text-muted">Arquivo atual: <span id="current-file">-</span></small>
        <small class="text-muted">
          <span class="text-success">✓ <span id="success-count">0</span></span> |
          <span class="text-danger">✗ <span id="error-count">0</span></span>
        </small>
      </div>
    </div>
  `;
}

/**
 * Atualiza a barra de progresso
 */
function updateProgressBar(
  processed,
  total,
  currentFile,
  successCount,
  errorCount,
) {
  const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

  // Atualizar barra de progresso
  const progressBar = document.querySelector('.progress-bar');
  if (progressBar) {
    progressBar.style.width = `${percentage}%`;
    progressBar.setAttribute('aria-valuenow', percentage);
    progressBar.textContent = `${percentage}%`;
  }

  // Atualizar contador
  const badge = document.querySelector('.badge');
  if (badge) {
    badge.textContent = `${processed}/${total}`;
  }

  // Atualizar arquivo atual
  const currentFileSpan = document.getElementById('current-file');
  if (currentFileSpan && currentFile) {
    currentFileSpan.textContent = currentFile;
  }

  // Atualizar contadores de sucesso e erro
  const successSpan = document.getElementById('success-count');
  const errorSpan = document.getElementById('error-count');
  if (successSpan) successSpan.textContent = successCount || 0;
  if (errorSpan) errorSpan.textContent = errorCount || 0;
}

/**
 * Trata a conclusão da importação
 */
function handleImportComplete(data) {
  const results = data.results;
  const total = results.total;
  const success = results.success.length;
  const errors = results.errors.length;
  const invalid = data.invalid_files ? data.invalid_files.length : 0;

  let message = `Importação em lote concluída:<br>`;
  message += `- ${success} arquivo(s) importado(s) com sucesso<br>`;
  if (errors > 0) message += `- ${errors} arquivo(s) com erro<br>`;
  if (invalid > 0) message += `- ${invalid} arquivo(s) inválido(s)<br>`;

  showImportResult(errors === 0 ? 'success' : 'warning', message);

  // Recarregar dados e limpar formulário
  loadImportacaoData();
  const form = document.getElementById('import-form');
  if (form) form.reset();
}

/**
 * Trata erros da importação
 */
function handleImportError(data) {
  showImportResult('error', data.message || 'Erro durante a importação');
}

/**
 * Importa um arquivo SPED
 */
function importarSPED() {
  const form = document.getElementById('import-sped-form');
  const resultDiv = document.getElementById('import-sped-result');
  const fileInput = document.getElementById('sped-file');

  if (!form || !resultDiv || !fileInput) return;

  // Verificar se arquivo foi selecionado
  if (!fileInput.files || fileInput.files.length === 0) {
    showImportResult(
      'error',
      'Selecione um arquivo SPED',
      'import-sped-result',
    );
    return;
  }

  // Criar FormData para envio do arquivo
  const formData = new FormData();
  formData.append('arquivo', fileInput.files[0]);

  // Mostrar indicador de carregamento
  showImportResult(
    'loading',
    'Importando arquivo SPED...',
    'import-sped-result',
  );

  // Enviar o arquivo para a API
  fetch('/api/importacoes/sped', {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: formData,
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success || data.importacao) {
        const totais = data.totais || {};
        let message = 'SPED importado com sucesso!<br>';
        message += `- ${totais.notas || 0} nota(s) de entrada<br>`;
        message += `- ${totais.itens || 0} item(ns)<br>`;
        message += `- ${totais.clientes || 0} fornecedor(es)<br>`;
        message += `- ${totais.produtos || 0} produto(s)`;

        showImportResult('success', message, 'import-sped-result');
        loadImportacaoSpedData();
        form.reset();
      } else {
        showImportResult(
          'error',
          data.message || 'Erro ao importar SPED',
          'import-sped-result',
        );
      }
    })
    .catch((error) => {
      console.error('Erro ao importar SPED:', error);
      showImportResult(
        'error',
        'Erro ao processar solicitação',
        'import-sped-result',
      );
    });
}

/**
 * Carrega os dados de importações XML
 */
function loadImportacaoXmlData() {
  const importacoesContent = document.getElementById('importacoes-xml-content');
  if (!importacoesContent) return;

  // Mostrar indicador de carregamento
  importacoesContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  `;

  // Obter importações da API
  fetch('/api/importacoes', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.importacoes && data.importacoes.length > 0) {
        // Renderizar tabela de importações XML
        renderImportacoesXmlTable(importacoesContent, data.importacoes);
      } else {
        // Mostrar mensagem de nenhuma importação
        importacoesContent.innerHTML = `
          <div class="alert alert-info">
            Nenhuma importação XML encontrada.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar importações XML:', error);
      importacoesContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar importações XML: ${error.message}
        </div>
      `;
    });
}

/**
 * Carrega os dados de importações SPED
 */
function loadImportacaoSpedData() {
  const importacoesContent = document.getElementById(
    'importacoes-sped-content',
  );
  if (!importacoesContent) return;

  // Mostrar indicador de carregamento
  importacoesContent.innerHTML = `
    <div class="text-center py-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>
  `;

  // Obter importações da API
  fetch('/api/importacoes/sped/historico', {
    headers: {
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.importacoes && data.importacoes.length > 0) {
        // Renderizar tabela de importações SPED
        renderImportacoesSpedTable(importacoesContent, data.importacoes);
      } else {
        // Mostrar mensagem de nenhuma importação
        importacoesContent.innerHTML = `
          <div class="alert alert-info">
            Nenhuma importação SPED encontrada.
          </div>
        `;
      }
    })
    .catch((error) => {
      console.error('Erro ao carregar importações SPED:', error);
      importacoesContent.innerHTML = `
        <div class="alert alert-danger">
          Erro ao carregar importações SPED: ${error.message}
        </div>
      `;
    });
}

/**
 * Renderiza a tabela de importações XML
 */
function renderImportacoesXmlTable(container, importacoes) {
  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="importacoes-xml-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>ID</th>
            <th>Arquivo</th>
            <th>Nota Fiscal</th>
            <th>Data Emissão</th>
            <th>Data Importação</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  importacoes.forEach((importacao) => {
    const dataEmissao = importacao.data_emissao
      ? new Date(importacao.data_emissao).toLocaleDateString('pt-BR')
      : '-';
    const dataImportacao = importacao.data_importacao
      ? new Date(importacao.data_importacao).toLocaleDateString('pt-BR')
      : '-';

    const statusClass =
      importacao.status === 'concluido' ? 'text-success' : 'text-danger';
    const statusIcon =
      importacao.status === 'concluido'
        ? '<i class="fas fa-check-circle"></i>'
        : '<i class="fas fa-exclamation-circle"></i>';

    html += `
      <tr>
        <td>${importacao.id}</td>
        <td>${importacao.arquivo_nome}</td>
        <td>${importacao.numero_nf || '-'}</td>
        <td>${dataEmissao}</td>
        <td>${dataImportacao}</td>
        <td class="${statusClass}">${statusIcon} ${
      importacao.status === 'concluido' ? 'Concluído' : 'Erro'
    }</td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable
  try {
    new DataTable('#importacoes-xml-table', {
      language: {
        url: '/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      order: [[0, 'desc']], // Ordenar por ID (mais recentes primeiro)
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable XML:', error);
  }
}

/**
 * Renderiza a tabela de importações SPED
 */
function renderImportacoesSpedTable(container, importacoes) {
  // Criar HTML da tabela
  let html = `
    <div class="table-responsive">
      <table id="importacoes-sped-table" class="table table-striped table-hover">
        <thead>
          <tr>
            <th>ID</th>
            <th>Arquivo</th>
            <th>Empresa</th>
            <th>Período</th>
            <th>Notas</th>
            <th>Itens</th>
            <th>Fornecedores</th>
            <th>Produtos</th>
            <th>Data Importação</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Adicionar linhas da tabela
  importacoes.forEach((importacao) => {
    const dataImportacao = importacao.data_importacao
      ? new Date(importacao.data_importacao).toLocaleDateString('pt-BR')
      : '-';

    const dataInicio = importacao.data_inicio
      ? new Date(importacao.data_inicio).toLocaleDateString('pt-BR')
      : '';
    const dataFim = importacao.data_fim
      ? new Date(importacao.data_fim).toLocaleDateString('pt-BR')
      : '';
    const periodo = dataInicio && dataFim ? `${dataInicio} - ${dataFim}` : '-';

    const statusClass =
      importacao.status === 'concluido' ? 'text-success' : 'text-danger';
    const statusIcon =
      importacao.status === 'concluido'
        ? '<i class="fas fa-check-circle"></i>'
        : '<i class="fas fa-exclamation-circle"></i>';

    html += `
      <tr>
        <td>${importacao.id}</td>
        <td>${importacao.arquivo_nome}</td>
        <td>${importacao.razao_social_empresa || '-'}</td>
        <td>${periodo}</td>
        <td>${importacao.total_notas || 0}</td>
        <td>${importacao.total_itens || 0}</td>
        <td>${importacao.total_clientes || 0}</td>
        <td>${importacao.total_produtos || 0}</td>
        <td>${dataImportacao}</td>
        <td class="${statusClass}">${statusIcon} ${
      importacao.status === 'concluido' ? 'Concluído' : 'Erro'
    }</td>
      </tr>
    `;
  });

  html += `
        </tbody>
      </table>
    </div>
  `;

  // Atualizar o conteúdo
  container.innerHTML = html;

  // Inicializar DataTable
  try {
    new DataTable('#importacoes-sped-table', {
      language: {
        url: '/static/js/vendor/datatables/pt-BR.json',
      },
      responsive: true,
      order: [[0, 'desc']], // Ordenar por ID (mais recentes primeiro)
    });
  } catch (error) {
    console.error('Erro ao inicializar DataTable SPED:', error);
  }
}
