/* ===== CHATBOT IA STYLES ===== */

/* Container principal do chatbot */
.chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-height: calc(100vh - 100px);
    overflow: visible;
}

/* Botão flutuante para abrir o chatbot */
.chatbot-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
}

.chatbot-toggle.active {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* <PERSON>la do chatbot */
.chatbot-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    max-height: calc(100vh - 120px);
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    display: none;
    flex-direction: column;
    overflow: hidden;
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header do chatbot */
.chatbot-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    text-align: center;
    position: relative;
}

.chatbot-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chatbot-header .subtitle {
    font-size: 11px;
    opacity: 0.9;
    margin-top: 3px;
}

.chatbot-close {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s;
}

.chatbot-close:hover {
    opacity: 1;
}

/* Área de mensagens */
.chatbot-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 12px;
    max-height: 300px;
}

/* Mensagem individual */
.message {
    max-width: 85%;
    padding: 10px 14px;
    border-radius: 16px;
    font-size: 13px;
    line-height: 1.4;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    align-self: flex-end;
    margin-left: auto;
}

.message.bot {
    background: white;
    color: #333;
    align-self: flex-start;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.bot .message-content {
    white-space: pre-wrap;
}

/* Indicador de digitação */
.typing-indicator {
    display: none;
    align-self: flex-start;
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    border: 1px solid #e9ecef;
    max-width: 80px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Área de input */
.chatbot-input-area {
    padding: 15px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chatbot-input-container {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.chatbot-input {
    flex: 1;
    border: 2px solid #e9ecef;
    border-radius: 20px;
    padding: 12px 16px;
    font-size: 14px;
    resize: none;
    max-height: 100px;
    min-height: 44px;
    outline: none;
    transition: border-color 0.2s;
}

.chatbot-input:focus {
    border-color: #667eea;
}

.chatbot-send {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-send:hover:not(:disabled) {
    transform: scale(1.05);
}

.chatbot-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Sugestões de perguntas */
.chatbot-suggestions {
    padding: 12px 15px;
    border-top: 1px solid #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    max-height: 120px;
    overflow-y: auto;
}

.suggestions-header {
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.suggestions-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.suggestion-chip {
    display: inline-flex;
    align-items: center;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 12px;
    padding: 6px 10px;
    font-size: 11px;
    line-height: 1.2;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    word-break: break-word;
    text-align: left;
    color: #495057;
}

.suggestion-chip:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
    border-color: transparent;
}

.suggestion-chip:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(102, 126, 234, 0.2);
}

/* Responsividade para sugestões */
@media (max-width: 400px) {
    .suggestion-chip {
        font-size: 10px;
        padding: 5px 8px;
        flex: 1 1 calc(50% - 3px);
        min-width: 0;
    }

    .suggestions-grid {
        gap: 4px;
    }
}

/* Status do chatbot */
.chatbot-status {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

/* Avaliação de resposta */
.message-rating {
    margin-top: 10px;
    display: flex;
    gap: 5px;
    align-items: center;
}

.rating-star {
    color: #ddd;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.2s;
}

.rating-star:hover,
.rating-star.active {
    color: #ffc107;
}

/* Responsividade */
@media (max-width: 768px) {
    .chatbot-window {
        width: 340px;
        height: 450px;
        bottom: 70px;
        right: -10px;
    }

    .chatbot-toggle {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .chatbot-window {
        width: calc(100vw - 30px);
        height: 60vh;
        max-height: 400px;
        bottom: 70px;
        right: 15px;
        left: 15px;
    }

    .chatbot-messages {
        max-height: 250px;
    }
}

/* Dark mode support */
[data-theme="dark"] .chatbot-window {
    background: #2d3748;
}

[data-theme="dark"] .chatbot-messages {
    background: #1a202c;
}

[data-theme="dark"] .message.bot {
    background: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
}

[data-theme="dark"] .chatbot-input-area {
    background: #2d3748;
    border-color: #4a5568;
}

[data-theme="dark"] .chatbot-input {
    background: #1a202c;
    color: #e2e8f0;
    border-color: #4a5568;
}

[data-theme="dark"] .suggestion-chip {
    background: #4a5568;
    color: #e2e8f0;
    border-color: #718096;
}

[data-theme="dark"] .chatbot-status {
    background: #2d3748;
    border-color: #4a5568;
    color: #a0aec0;
}
