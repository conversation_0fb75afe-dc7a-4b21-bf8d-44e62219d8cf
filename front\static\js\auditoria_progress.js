/**
 * Gerenciador de progresso da auditoria em tempo real via WebSocket
 */

class AuditoriaProgressManager {
  constructor() {
    this.socket = null;
    this.currentAuditId = null;
    this.isConnected = false;
  }

  /**
   * Inicia o acompanhamento de progresso da auditoria
   * @param {string} auditId - ID único da auditoria
   * @param {number} totalTributos - Total de tributos a serem auditados
   */
  startAuditProgress(auditId, totalTributos = 0) {
    this.currentAuditId = auditId;
    
    // Conectar ao WebSocket se não estiver conectado
    if (!this.socket || !this.isConnected) {
      this.connectWebSocket();
    }

    // Aguardar um pouco para garantir que a conexão WebSocket esteja estabelecida
    setTimeout(() => {
      this.joinAuditRoom(auditId);
      this.showProgressBar(0, totalTributos);
    }, 100);
  }

  /**
   * Conecta ao WebSocket
   */
  connectWebSocket() {
    try {
      // Verificar se Socket.IO está disponível
      if (typeof io === 'undefined') {
        console.error('Socket.IO não está carregado');
        return;
      }

      // Conectar ao servidor WebSocket
      this.socket = io('http://127.0.0.1:5000', {
        transports: ['websocket', 'polling']
      });

      // Configurar eventos do socket
      this.setupSocketEvents();

    } catch (error) {
      console.error('Erro ao conectar WebSocket:', error);
    }
  }

  /**
   * Configura os eventos do WebSocket
   */
  setupSocketEvents() {
    this.socket.on('connect', () => {
      console.log('WebSocket conectado para auditoria');
      this.isConnected = true;
    });

    this.socket.on('disconnect', () => {
      console.log('WebSocket desconectado');
      this.isConnected = false;
    });

    this.socket.on('audit_progress', (data) => {
      this.updateProgressBar(data);
    });

    this.socket.on('audit_complete', (data) => {
      this.handleAuditComplete(data);
    });

    this.socket.on('audit_error', (data) => {
      this.handleAuditError(data);
    });
  }

  /**
   * Entra na sala da auditoria
   * @param {string} auditId - ID da auditoria
   */
  joinAuditRoom(auditId) {
    if (!this.socket || !this.isConnected) {
      console.warn('Socket não conectado');
      return;
    }

    this.socket.emit('join_audit', {
      token: localStorage.getItem('token'),
      audit_id: auditId,
    });

    console.log(`Entrando na sala de auditoria: ${auditId}`);
  }

  /**
   * Mostra a barra de progresso
   * @param {number} processed - Número de tributos processados
   * @param {number} total - Total de tributos
   */
  showProgressBar(processed, total) {
    const resultDiv = document.getElementById('audit-result') || this.createResultDiv();
    
    const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;

    resultDiv.className = 'mt-3 alert alert-info';
    resultDiv.innerHTML = `
      <div class="d-flex flex-column">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <span><strong>Executando auditoria...</strong></span>
          <span class="badge bg-primary">${processed}/${total}</span>
        </div>
        <div class="progress mb-2" style="height: 20px;">
          <div class="progress-bar progress-bar-striped progress-bar-animated"
               role="progressbar"
               style="width: ${percentage}%"
               aria-valuenow="${percentage}"
               aria-valuemin="0"
               aria-valuemax="100">
            ${percentage}%
          </div>
        </div>
        <div class="d-flex justify-content-between">
          <small class="text-muted">Tributo atual: <span id="current-tributo">-</span></small>
          <small class="text-muted">
            <span class="text-success">Auditados: <span id="audit-success-count">0</span></span> |
            <span class="text-danger">Não auditados: <span id="audit-error-count">0</span></span>
          </small>
        </div>
      </div>
    `;
  }

  /**
   * Atualiza a barra de progresso
   * @param {Object} data - Dados do progresso
   */
  updateProgressBar(data) {
    const percentage = data.percentage || 0;
    const processed = data.processed || 0;
    const total = data.total || 0;

    // Atualizar barra de progresso
    const progressBar = document.querySelector('.progress-bar');
    if (progressBar) {
      progressBar.style.width = `${percentage}%`;
      progressBar.setAttribute('aria-valuenow', percentage);
      progressBar.textContent = `${percentage}%`;
    }

    // Atualizar contador
    const badge = document.querySelector('.badge');
    if (badge) {
      badge.textContent = `${processed}/${total}`;
    }

    // Atualizar tributo atual
    const currentTributoSpan = document.getElementById('current-tributo');
    if (currentTributoSpan && data.current_tributo_id) {
      currentTributoSpan.textContent = `ID ${data.current_tributo_id}`;
    }

    // Atualizar contadores
    const successCountSpan = document.getElementById('audit-success-count');
    if (successCountSpan) {
      successCountSpan.textContent = data.auditados || 0;
    }

    const errorCountSpan = document.getElementById('audit-error-count');
    if (errorCountSpan) {
      errorCountSpan.textContent = data.nao_auditados || 0;
    }

    console.log('Progresso da auditoria atualizado:', data);
  }

  /**
   * Trata a conclusão da auditoria
   * @param {Object} data - Dados da conclusão
   */
  handleAuditComplete(data) {
    const results = data.results;
    
    if (results && results.success) {
      const total = results.total;
      const auditados = results.auditados;
      const naoAuditados = results.nao_auditados;

      // Mostrar resultado final sem alert
      this.showAuditResult('success', `
        Auditoria concluída com sucesso:<br>
        - ${total} tributo(s) processado(s)<br>
        - ${auditados} tributo(s) auditado(s)<br>
        - ${naoAuditados} tributo(s) não auditado(s)
      `);
    } else {
      this.showAuditResult('warning', 'Auditoria concluída com problemas');
    }

    // Recarregar dashboard se estiver na página de auditoria
    if (typeof carregarDashboardAuditoria === 'function') {
      const tipoTributo = this.getCurrentTipoTributo();
      if (tipoTributo) {
        setTimeout(() => carregarDashboardAuditoria(tipoTributo), 1000);
      }
    }

    // Desconectar WebSocket
    this.disconnect();
  }

  /**
   * Trata erros na auditoria
   * @param {Object} data - Dados do erro
   */
  handleAuditError(data) {
    this.showAuditResult('danger', `Erro na auditoria: ${data.message}`);
    this.disconnect();
  }

  /**
   * Mostra o resultado da auditoria
   * @param {string} type - Tipo do alert (success, warning, danger)
   * @param {string} message - Mensagem a ser exibida
   */
  showAuditResult(type, message) {
    const resultDiv = document.getElementById('audit-result') || this.createResultDiv();
    
    resultDiv.className = `mt-3 alert alert-${type}`;
    resultDiv.innerHTML = message;

    // Auto-remover após 5 segundos se for sucesso
    if (type === 'success') {
      setTimeout(() => {
        if (resultDiv.parentNode) {
          resultDiv.remove();
        }
      }, 5000);
    }
  }

  /**
   * Cria o div de resultado se não existir
   */
  createResultDiv() {
    let resultDiv = document.getElementById('audit-result');
    if (!resultDiv) {
      resultDiv = document.createElement('div');
      resultDiv.id = 'audit-result';
      
      // Tentar inserir após o botão de executar auditoria
      const auditButton = document.getElementById('btn-executar-auditoria');
      if (auditButton && auditButton.parentNode) {
        auditButton.parentNode.insertBefore(resultDiv, auditButton.nextSibling);
      } else {
        // Fallback: inserir no container da auditoria
        const container = document.getElementById('auditoria-dashboard-container') || document.body;
        container.insertBefore(resultDiv, container.firstChild);
      }
    }
    return resultDiv;
  }

  /**
   * Obtém o tipo de tributo atual da URL
   */
  getCurrentTipoTributo() {
    const path = window.location.pathname;
    const match = path.match(/\/auditoria\/(?:entrada|saida)\/(.+)/);
    return match ? match[1] : null;
  }

  /**
   * Desconecta o WebSocket
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.isConnected = false;
      this.currentAuditId = null;
    }
  }
}

// Instância global do gerenciador de progresso
window.auditoriaProgressManager = new AuditoriaProgressManager();
