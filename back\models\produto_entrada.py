from .escritorio import db
from sqlalchemy.sql import func


class ProdutoEntrada(db.Model):
    __tablename__ = 'produto_entrada'
    
    id = db.Column(db.Integer, primary_key=True)
    empresa_id = db.Column(db.<PERSON><PERSON>ger, db.<PERSON><PERSON>('empresa.id'), nullable=False)
    escritorio_id = db.Column(db.Integer, db.ForeignKey('escritorio.id'), nullable=False)
    cod_item = db.Column(db.String(50), nullable=False)  # Código do item no SPED
    descricao = db.Column(db.String(255), nullable=False)
    codigo_barra = db.Column(db.String(50))
    codigo_anterior = db.Column(db.String(50))
    unidade = db.Column(db.String(10))
    tipo_item = db.Column(db.String(2))  # 01=Mercadoria, 02=Matéria-prima, etc
    ncm = db.Column(db.String(20))
    ex_ipi = db.Column(db.String(10))
    codigo_genero = db.Column(db.String(10))
    codigo_lst = db.Column(db.String(10))
    aliquota_icms = db.Column(db.Numeric(5, 2))
    cest = db.Column(db.String(10))
    data_cadastro = db.Column(db.DateTime, server_default=func.now())
    
    # Relacionamentos
    itens_nota = db.relationship('ItemNotaEntrada', backref='produto_entrada', lazy=True)
    
    # Constraint única
    __table_args__ = (
        db.UniqueConstraint('empresa_id', 'cod_item', name='uq_produto_entrada_empresa_cod_item'),
    )
    
    def __repr__(self):
        return f"<ProdutoEntrada {self.cod_item} - {self.descricao}>"
    
    def to_dict(self):
        """Convert the model instance to a dictionary"""
        return {
            'id': self.id,
            'empresa_id': self.empresa_id,
            'escritorio_id': self.escritorio_id,
            'cod_item': self.cod_item,
            'descricao': self.descricao,
            'codigo_barra': self.codigo_barra,
            'codigo_anterior': self.codigo_anterior,
            'unidade': self.unidade,
            'tipo_item': self.tipo_item,
            'ncm': self.ncm,
            'ex_ipi': self.ex_ipi,
            'codigo_genero': self.codigo_genero,
            'codigo_lst': self.codigo_lst,
            'aliquota_icms': float(self.aliquota_icms) if self.aliquota_icms else None,
            'cest': self.cest,
            'data_cadastro': self.data_cadastro.isoformat() if self.data_cadastro else None
        }
