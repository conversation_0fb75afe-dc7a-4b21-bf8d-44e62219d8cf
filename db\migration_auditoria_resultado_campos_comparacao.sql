-- Migração para adicionar campos de comparação fiscal na tabela auditoria_resultado
-- Data: 2024-12-19
-- Descrição: Adiciona campos para armazenar CST, origem e alíquotas da nota e cenário,
--           além de flags para indicar tipos específicos de inconsistências

-- Adicionar campos para comparação de dados fiscais
ALTER TABLE auditoria_resultado 
ADD COLUMN IF NOT EXISTS cst_nota VARCHAR(3),
ADD COLUMN IF NOT EXISTS cst_cenario VARCHAR(3),
ADD COLUMN IF NOT EXISTS origem_nota VARCHAR(2),
ADD COLUMN IF NOT EXISTS origem_cenario VARCHAR(2),
ADD COLUMN IF NOT EXISTS aliquota_nota DECIMAL(10, 4),
ADD COLUMN IF NOT EXISTS aliquota_cenario DECIMAL(10, 4);

-- Adicionar campos para indicar tipos de inconsistências
ALTER TABLE auditoria_resultado 
ADD COLUMN IF NOT EXISTS inconsistencia_valor BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inconsistencia_cst BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inconsistencia_origem BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inconsistencia_aliquota BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS inconsistencia_base_calculo BOOLEAN DEFAULT FALSE;

-- Comentários nas colunas para documentação
COMMENT ON COLUMN auditoria_resultado.cst_nota IS 'CST da nota fiscal';
COMMENT ON COLUMN auditoria_resultado.cst_cenario IS 'CST do cenário aplicado';
COMMENT ON COLUMN auditoria_resultado.origem_nota IS 'Origem da mercadoria na nota fiscal';
COMMENT ON COLUMN auditoria_resultado.origem_cenario IS 'Origem da mercadoria no cenário';
COMMENT ON COLUMN auditoria_resultado.aliquota_nota IS 'Alíquota aplicada na nota fiscal';
COMMENT ON COLUMN auditoria_resultado.aliquota_cenario IS 'Alíquota do cenário aplicado';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_valor IS 'Indica se há inconsistência no valor do tributo';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_cst IS 'Indica se há inconsistência no CST';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_origem IS 'Indica se há inconsistência na origem';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_aliquota IS 'Indica se há inconsistência na alíquota';
COMMENT ON COLUMN auditoria_resultado.inconsistencia_base_calculo IS 'Indica se há inconsistência na base de cálculo';

-- Criar índices para melhorar performance das consultas
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_inconsistencia_cst 
ON auditoria_resultado(inconsistencia_cst) WHERE inconsistencia_cst = TRUE;

CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_inconsistencia_origem 
ON auditoria_resultado(inconsistencia_origem) WHERE inconsistencia_origem = TRUE;

CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_inconsistencia_aliquota 
ON auditoria_resultado(inconsistencia_aliquota) WHERE inconsistencia_aliquota = TRUE;

CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_cst_nota 
ON auditoria_resultado(cst_nota);

CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_cst_cenario 
ON auditoria_resultado(cst_cenario);

-- Atualizar registros existentes com valores padrão para os novos campos booleanos
UPDATE auditoria_resultado 
SET 
    inconsistencia_valor = (status = 'inconsistente'),
    inconsistencia_cst = FALSE,
    inconsistencia_origem = FALSE,
    inconsistencia_aliquota = FALSE,
    inconsistencia_base_calculo = FALSE
WHERE 
    inconsistencia_valor IS NULL;

COMMIT;
