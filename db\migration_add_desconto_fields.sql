-- Migration para adicionar campos de desconto
-- Data: 2024-12-19
-- Descrição: Adiciona campos para processar desconto nos tributos e cenários

-- Adicionar campo valor_desconto na tabela tributo
ALTER TABLE tributo ADD COLUMN IF NOT EXISTS valor_desconto DECIMAL(10, 2);

-- Adici<PERSON>r campo incluir_desconto nas tabelas de cenários
ALTER TABLE cenario_icms ADD COLUMN IF NOT EXISTS incluir_desconto BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_icms_st ADD COLUMN IF NOT EXISTS incluir_desconto BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_ipi ADD COLUMN IF NOT EXISTS incluir_desconto BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_pis ADD COLUMN IF NOT EXISTS incluir_desconto BOOLEAN DEFAULT TRUE;
ALTER TABLE cenario_cofins ADD COLUMN IF NOT EXISTS incluir_desconto BOOLEAN DEFAULT TRUE;

-- Comentários para documentação
COMMENT ON COLUMN tributo.valor_desconto IS 'Valor do desconto proporcional ao item da nota fiscal';
COMMENT ON COLUMN cenario_icms.incluir_desconto IS 'Define se o desconto deve ser subtraído da base de cálculo do ICMS';
COMMENT ON COLUMN cenario_icms_st.incluir_desconto IS 'Define se o desconto deve ser subtraído da base de cálculo do ICMS-ST';
COMMENT ON COLUMN cenario_ipi.incluir_desconto IS 'Define se o desconto deve ser subtraído da base de cálculo do IPI';
COMMENT ON COLUMN cenario_pis.incluir_desconto IS 'Define se o desconto deve ser subtraído da base de cálculo do PIS';
COMMENT ON COLUMN cenario_cofins.incluir_desconto IS 'Define se o desconto deve ser subtraído da base de cálculo do COFINS';
