# Guia de Implementação: Autenticação Centralizada

## Visão Geral
Este documento descreve os passos necessários para implementar um sistema de autenticação centralizada usando JWT, permitindo que múltiplos sistemas compartilhem o mesmo serviço de autenticação.

## 1. Estrutura do Projeto

### 1.1 Serviço de Autenticação (Auth Service)
```
auth-service/
├── src/
│   ├── controllers/
│   │   ├── auth.controller.js
│   │   ├── office.controller.js
│   │   └── user.controller.js
│   ├── middlewares/
│   │   ├── auth.middleware.js
│   │   └── permissions.middleware.js
│   ├── models/
│   │   ├── Office.js
│   │   ├── User.js
│   │   └── Plan.js
│   ├── routes/
│   │   ├── auth.routes.js
│   │   ├── office.routes.js
│   │   └── user.routes.js
│   └── services/
│       ├── auth.service.js
│       └── jwt.service.js
├── .env
└── package.json
```

### 1.2 Sistema de Auditoria (Modificações)
```
auditoria-fiscal/
├── back/
│   ├── middlewares/
│   │   └── auth.middleware.js
│   └── services/
│       └── auth.service.js
└── front/
    ├── src/
    │   ├── services/
    │   │   └── api.js
    └── public/
        └── js/
            └── auth.js
```

## 2. Fluxo de Autenticação

1. Usuário acessa o sistema de auditoria
2. Frontend redireciona para o Auth Service
3. Usuário faz login no Auth Service
4. Auth Service gera tokens JWT e redireciona de volta para o sistema
5. Sistema valida o token JWT em cada requisição

## 3. Estrutura do Token JWT

```json
{
  "sub": "user_123",
  "office_id": "office_456",
  "role": "admin_escritorio",
  "plan_status": "active",
  "permissions": ["empresas:read", "empresas:write", "usuarios:create"],
  "iat": 1622736000,
  "exp": 1622739600
}
```

## 4. Endpoints do Serviço de Autenticação

### 4.1 Autenticação
- `POST /auth/login` - Login de usuário
- `POST /auth/refresh-token` - Renovar token de acesso
- `POST /auth/validate-token` - Validar token
- `POST /auth/logout` - Invalidar token

### 4.2 Gerenciamento de Usuários (apenas admin_master e admin_escritorio)
- `POST /users` - Criar novo usuário (analista)
- `GET /users` - Listar usuários do escritório
- `GET /users/:id` - Obter detalhes de um usuário
- `PUT /users/:id` - Atualizar usuário
- `DELETE /users/:id` - Desativar usuário

### 4.3 Gerenciamento de Escritórios (apenas admin_master)
- `POST /offices` - Criar novo escritório
- `GET /offices` - Listar escritórios
- `GET /offices/:id` - Obter detalhes de um escritório
- `PUT /offices/:id` - Atualizar escritório
- `PUT /offices/:id/plan` - Atualizar plano do escritório

## 5. Implementação no Sistema de Auditoria

### 5.1 Backend

#### 5.1.1 Middleware de Autenticação
```javascript
// back/middlewares/auth.middleware.js
const jwt = require('jsonwebtoken');
const axios = require('axios');

const AUTH_SERVICE_URL = process.env.AUTH_SERVICE_URL;
const AUTH_SERVICE_SECRET = process.env.AUTH_SERVICE_SECRET;

const authenticateJWT = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Token não fornecido' });
  }

  const token = authHeader.split(' ')[1];
  
  try {
    // Verificar assinatura do token
    const decoded = jwt.verify(token, AUTH_SERVICE_SECRET);
    
    // Verificar se o token está expirado
    if (decoded.exp < Date.now() / 1000) {
      return res.status(401).json({ error: 'Token expirado' });
    }
    
    // Verificar se o usuário tem acesso ao sistema de auditoria
    if (!decoded.permissions.includes('auditoria:access')) {
      return res.status(403).json({ error: 'Acesso negado' });
    }
    
    // Verificar status do plano
    if (decoded.plan_status !== 'active') {
      return res.status(403).json({ 
        error: 'Plano inativo',
        plan_status: decoded.plan_status
      });
    }
    
    // Adicionar informações do usuário à requisição
    req.user = {
      id: decoded.sub,
      office_id: decoded.office_id,
      role: decoded.role,
      permissions: decoded.permissions
    };
    
    next();
  } catch (error) {
    console.error('Erro na autenticação:', error);
    return res.status(401).json({ error: 'Token inválido' });
  }
};

// Middleware para verificar permissões específicas
const checkPermission = (requiredPermission) => {
  return (req, res, next) => {
    if (!req.user || !req.user.permissions.includes(requiredPermission)) {
      return res.status(403).json({ error: 'Permissão negada' });
    }
    next();
  };
};

module.exports = {
  authenticateJWT,
  checkPermission
};
```

### 5.2 Frontend

#### 5.2.1 Serviço de Autenticação
```javascript
// front/public/js/auth.js
class AuthService {
  constructor() {
    this.AUTH_SERVICE_URL = 'https://auth.seudominio.com';
    this.AUDIT_APP_URL = 'https://auditoria.seudominio.com';
    this.token = localStorage.getItem('auth_token');
    this.refreshToken = localStorage.getItem('refresh_token');
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
  }

  async login(credentials) {
    try {
      const response = await fetch(`${this.AUTH_SERVICE_URL}/auth/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...credentials,
          redirect_uri: `${this.AUDIT_APP_URL}/auth/callback`
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Falha no login');
      }

      const { token, refresh_token, user } = await response.json();
      
      this.setSession(token, refresh_token, user);
      return user;
    } catch (error) {
      console.error('Erro no login:', error);
      throw error;
    }
  }

  setSession(token, refreshToken, user) {
    this.token = token;
    this.refreshToken = refreshToken;
    this.user = user;
    
    localStorage.setItem('auth_token', token);
    localStorage.setItem('refresh_token', refreshToken);
    localStorage.setItem('user', JSON.stringify(user));
    
    // Configurar cabeçalhos padrão para todas as requisições
    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  logout() {
    // Limpar dados locais
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    
    // Limpar cabeçalhos
    delete axios.defaults.headers.common['Authorization'];
    
    // Redirecionar para o serviço de autenticação para logout
    window.location.href = `${this.AUTH_SERVICE_URL}/auth/logout?redirect_uri=${encodeURIComponent(this.AUDIT_APP_URL)}`;
  }

  async refreshSession() {
    try {
      const response = await fetch(`${this.AUTH_SERVICE_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refresh_token: this.refreshToken
        })
      });

      if (!response.ok) {
        throw new Error('Falha ao renovar sessão');
      }

      const { token, refresh_token, user } = await response.json();
      this.setSession(token, refresh_token, user);
      return { token, user };
    } catch (error) {
      console.error('Erro ao renovar sessão:', error);
      this.logout();
      throw error;
    }
  }

  isAuthenticated() {
    return !!this.token;
  }

  hasPermission(permission) {
    return this.user?.permissions?.includes(permission) || false;
  }

  // Interceptor para renovar token quando expirar
  setupInterceptors(axiosInstance) {
    axiosInstance.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;
        
        // Se o erro for 401 e não for uma tentativa de refresh
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;
          
          try {
            const { token } = await this.refreshSession();
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return axiosInstance(originalRequest);
          } catch (error) {
            this.logout();
            return Promise.reject(error);
          }
        }
        
        return Promise.reject(error);
      }
    );
  }
}

// Exportar instância única
export const authService = new AuthService();
```

## 6. Fluxo de Criação de Usuário por Admin do Escritório

1. Admin do escritório acessa a seção de usuários
2. Sistema verifica permissão `usuarios:create`
3. Formulário coleta dados do novo analista
4. Requisição é enviada para o Auth Service
5. Auth Service cria o usuário com role "analista" e vincula ao escritório
6. Email de boas-vindas é enviado com link de ativação

## 7. Controle de Acesso Baseado em Funções (RBAC)

### 7.1 Funções e Permissões

#### admin_master
- Pode gerenciar todos os escritórios
- Pode criar novos escritórios
- Pode alterar planos
- Acesso total ao sistema

#### admin_escritorio
- Pode gerenciar usuários do próprio escritório
- Pode criar/editar/desativar analistas
- Acesso total às funcionalidades do escritório
- Não pode alterar configurações de plano

#### analista
- Acesso limitado às funcionalidades atribuídas
- Pode visualizar/editar apenas os recursos permitidos
- Não pode gerenciar usuários

## 8. Sincronização de Escritórios e Usuários

### 8.1 Modelo de Dados para Escritório Local

```python
# models/escritorio.py
class Escritorio(db.Model):
    __tablename__ = 'escritorios'
    
    id = db.Column(db.String(36), primary_key=True)  # Mesmo ID do serviço de autenticação
    nome = db.Column(db.String(255), nullable=False)
    cnpj = db.Column(db.String(20), unique=True)
    ativo = db.Column(db.Boolean, default=True)
    data_cadastro = db.Column(db.DateTime, default=datetime.utcnow)
    data_atualizacao = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    plano_status = db.Column(db.String(20), default='active')
    
    # Relacionamentos
    usuarios = db.relationship('Usuario', backref='escritorio', lazy=True)
    empresas = db.relationship('Empresa', backref='escritorio', lazy=True)
```

### 8.2 Middleware de Sincronização

```python
# middlewares/sync_middleware.py
def sync_user_and_office(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'user') or not request.user:
            return jsonify({'error': 'Usuário não autenticado'}), 401
            
        user_info = request.user
        office_id = user_info.get('office_id')
        
        if not office_id:
            return jsonify({'error': 'Escritório não informado no token'}), 400
            
        try:
            # Sincroniza escritório
            escritorio = Escritorio.query.get(office_id)
            
            if not escritorio:
                office_data = get_office_from_auth_service(office_id, user_info.get('token'))
                escritorio = Escritorio(
                    id=office_id,
                    nome=office_data.get('nome'),
                    cnpj=office_data.get('cnpj'),
                    plano_status=user_info.get('plan_status', 'inactive')
                )
                db.session.add(escritorio)
                db.session.commit()
            
            # Sincroniza usuário
            usuario = Usuario.query.get(user_info.get('id'))
            if not usuario:
                usuario = Usuario(
                    id=user_info.get('id'),
                    nome=user_info.get('name'),
                    email=user_info.get('email'),
                    escritorio_id=office_id,
                    ativo=True,
                    role=user_info.get('role', 'analista')
                )
                db.session.add(usuario)
                db.session.commit()
            
            # Adiciona ao request para uso nas rotas
            request.escritorio = escritorio
            request.usuario = usuario
            
            return f(*args, **kwargs)
            
        except Exception as e:
            current_app.logger.error(f'Erro na sincronização: {str(e)}')
            return jsonify({'error': 'Erro ao sincronizar dados'}), 500
    
    return decorated_function
```

### 8.3 Uso nas Rotas

```python
@app.route('/api/empresas')
@authenticate_jwt
@sync_user_and_office
def listar_empresas():
    # Agora temos acesso ao escritório e usuário sincronizados
    empresas = Empresa.query.filter_by(escritorio_id=request.escritorio.id).all()
    return jsonify([e.to_dict() for e in empresas])
```

### 8.4 Fluxo de Sincronização

1. Usuário acessa o sistema pelo portal central
2. Token JWT é enviado para o sistema de auditoria
3. Middleware valida o token e extrai as informações
4. Sistema verifica se o escritório existe localmente
5. Se não existir, busca dados completos do serviço de autenticação
6. Cria/atualiza registro do escritório localmente
7. Sincroniza informações do usuário
8. Processa a requisição com os dados sincronizados

## 9. Segurança

1. **Tokens JWT**
   - Use chaves fortes para assinatura
   - Defina tempo de expiração curto (15-30 minutos)
   - Implemente renovação automática com refresh tokens

2. **HTTPS**
   - Sempre use HTTPS em produção
   - Habilite HSTS

3. **Proteção contra CSRF**
   - Use SameSite cookies
   - Implemente CSRF tokens para ações críticas

4. **Rate Limiting**
   - Implemente limitação de taxa para endpoints de autenticação
   - Bloqueie tentativas de força bruta

## 10. Monitoramento e Logs

1. **Logs de Autenticação**
   - Registre todas as tentativas de login
   - Monitore padrões suspeitos
   - Alerte sobre múltiplas falhas de autenticação

2. **Auditoria**
   - Registre todas as alterações em usuários e permissões
   - Mantenha histórico de alterações

## 11. Próximos Passos

1. Configurar ambiente do serviço de autenticação
2. Implementar os endpoints conforme documentado
3. Atualizar o sistema de auditoria para usar o novo serviço
4. Testar fluxos de autenticação e autorização
5. Implementar monitoramento e alertas
6. Documentar a API para desenvolvedores
7. Criar painel administrativo para gerenciamento de usuários e permissões
