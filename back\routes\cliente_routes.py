from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, Cliente, Empresa
from datetime import datetime

cliente_bp = Blueprint('cliente_bp', __name__)

@cliente_bp.route('/api/clientes', methods=['GET'])
@jwt_required()
def listar_clientes():
    """
    Lista os clientes com base nas permissões do usuário com suporte a paginação
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        status = request.args.get('status')
        uf = request.args.get('uf')
        municipio = request.args.get('municipio')
        atividade = request.args.get('atividade')
        destinacao = request.args.get('destinacao')
        cnae = request.args.get('cnae')

        # Parâmetros de paginação
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 100, type=int)  # Default 100 itens por página

        # Limitar o número máximo de itens por página para evitar sobrecarga
        if per_page > 500:
            per_page = 500

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = Cliente.query

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Filtrar por status se especificado
        if status:
            query = query.filter_by(status=status)

        # Aplicar filtros adicionais
        if uf:
            query = query.filter_by(uf=uf)
        if municipio:
            query = query.filter_by(municipio=municipio)
        if atividade:
            query = query.filter_by(atividade=atividade)
        if destinacao:
            query = query.filter_by(destinacao=destinacao)
        if cnae:
            query = query.filter_by(cnae=cnae)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os clientes
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem clientes do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas clientes das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(Cliente.empresa_id.in_(empresas_permitidas))

        # Contar total de registros para paginação
        total_count = query.count()

        # Aplicar paginação
        clientes_paginados = query.order_by(Cliente.id.desc()).paginate(page=page, per_page=per_page, error_out=False)

        # Converter para dicionários
        clientes_dict = [cliente.to_dict() for cliente in clientes_paginados.items]

        # Retornar dados com informações de paginação
        return jsonify({
            "clientes": clientes_dict,
            "pagination": {
                "total": total_count,
                "per_page": per_page,
                "current_page": page,
                "last_page": clientes_paginados.pages,
                "from": (page - 1) * per_page + 1 if clientes_paginados.items else 0,
                "to": (page - 1) * per_page + len(clientes_paginados.items) if clientes_paginados.items else 0,
                "has_prev": clientes_paginados.has_prev,
                "has_next": clientes_paginados.has_next,
                "prev_page": clientes_paginados.prev_num if clientes_paginados.has_prev else None,
                "next_page": clientes_paginados.next_num if clientes_paginados.has_next else None
            }
        }), 200

    except Exception as e:
        print(f"Erro ao listar clientes: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/<int:cliente_id>', methods=['GET'])
@jwt_required()
def obter_cliente(cliente_id):
    """
    Obtém os detalhes de um cliente específico
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o cliente
        cliente = db.session.get(Cliente, cliente_id)

        if not cliente:
            return jsonify({"message": "Cliente não encontrado"}), 404

        # Verificar permissões para visualizar o cliente
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer cliente
            pass
        elif usuario.tipo_usuario == 'escritorio' and cliente.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver clientes do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and cliente.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver clientes das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar este cliente"}), 403

        return jsonify({
            "cliente": cliente.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao obter cliente: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/<int:cliente_id>', methods=['PUT'])
@jwt_required()
def atualizar_cliente(cliente_id):
    """
    Atualiza os dados de um cliente
    """
    try:
        data = request.get_json()

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o cliente
        cliente = db.session.get(Cliente, cliente_id)

        if not cliente:
            return jsonify({"message": "Cliente não encontrado"}), 404

        # Verificar permissões para atualizar o cliente
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem atualizar qualquer cliente
            pass
        elif usuario.tipo_usuario == 'escritorio' and cliente.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem atualizar clientes do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and cliente.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem atualizar clientes das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para atualizar este cliente"}), 403

        # Atualizar os campos do cliente
        if 'razao_social' in data:
            cliente.razao_social = data['razao_social']
        if 'inscricao_estadual' in data:
            cliente.inscricao_estadual = data['inscricao_estadual']
        if 'logradouro' in data:
            cliente.logradouro = data['logradouro']
        if 'numero' in data:
            cliente.numero = data['numero']
        if 'bairro' in data:
            cliente.bairro = data['bairro']
        if 'municipio' in data:
            cliente.municipio = data['municipio']
        if 'uf' in data:
            cliente.uf = data['uf']
        if 'cep' in data:
            cliente.cep = data['cep']
        if 'cnae' in data:
            cliente.cnae = data['cnae']
        if 'atividade' in data:
            cliente.atividade = data['atividade']
        if 'destinacao' in data:
            cliente.destinacao = data['destinacao']
        if 'natureza_juridica' in data:
            cliente.natureza_juridica = data['natureza_juridica']
        if 'simples_nacional' in data:
            cliente.simples_nacional = data['simples_nacional']
        if 'status' in data:
            cliente.status = data['status']

        db.session.commit()

        return jsonify({
            "message": "Cliente atualizado com sucesso",
            "cliente": cliente.to_dict()
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao atualizar cliente: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes', methods=['POST'])
@jwt_required()
def criar_cliente():
    """
    Cria um novo cliente
    """
    try:
        data = request.get_json()

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Verificar se os campos obrigatórios estão presentes
        if not data.get('cnpj') or not data.get('razao_social') or not data.get('empresa_id'):
            return jsonify({"message": "CNPJ, Razão Social e ID da Empresa são obrigatórios"}), 400

        # Verificar se o usuário tem permissão para a empresa
        empresa_id = data.get('empresa_id')
        empresa = db.session.get(Empresa, empresa_id)

        if not empresa:
            return jsonify({"message": "Empresa não encontrada"}), 404

        # Verificar permissões para criar cliente na empresa
        if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                (usuario.empresas_permitidas and empresa_id in usuario.empresas_permitidas)):
            return jsonify({"message": "Você não tem permissão para criar clientes nesta empresa"}), 403

        # Verificar se já existe um cliente com o mesmo CNPJ na empresa
        cliente_existente = Cliente.query.filter_by(
            empresa_id=empresa_id,
            cnpj=data.get('cnpj')
        ).first()

        if cliente_existente:
            return jsonify({"message": "Já existe um cliente com este CNPJ nesta empresa"}), 400

        # Criar o novo cliente
        cliente = Cliente(
            empresa_id=empresa_id,
            escritorio_id=empresa.escritorio_id,
            cnpj=data.get('cnpj'),
            razao_social=data.get('razao_social'),
            inscricao_estadual=data.get('inscricao_estadual'),
            logradouro=data.get('logradouro'),
            numero=data.get('numero'),
            bairro=data.get('bairro'),
            municipio=data.get('municipio'),
            uf=data.get('uf'),
            cep=data.get('cep'),
            pais=data.get('pais', 'BRASIL'),
            codigo_pais=data.get('codigo_pais', '1058'),
            cnae=data.get('cnae'),
            atividade=data.get('atividade'),
            destinacao=data.get('destinacao'),
            natureza_juridica=data.get('natureza_juridica'),
            simples_nacional=data.get('simples_nacional', False),
            ind_ie_dest=data.get('ind_ie_dest'),
            ind_final=data.get('ind_final'),
            data_cadastro=datetime.now(),
            status=data.get('status', 'novo')
        )

        db.session.add(cliente)
        db.session.commit()

        return jsonify({
            "message": "Cliente criado com sucesso",
            "cliente": cliente.to_dict()
        }), 201

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao criar cliente: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/filter-options', methods=['GET'])
@jwt_required()
def obter_opcoes_filtro():
    """
    Obtém as opções para os filtros de clientes
    """
    try:
        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = Cliente.query

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os clientes
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem clientes do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas clientes das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(Cliente.empresa_id.in_(empresas_permitidas))

        # Obter valores distintos para cada filtro
        ufs = db.session.query(Cliente.uf).filter(Cliente.uf != None).distinct().all()
        atividades = db.session.query(Cliente.atividade).filter(Cliente.atividade != None).distinct().all()
        destinacoes = db.session.query(Cliente.destinacao).filter(Cliente.destinacao != None).distinct().all()
        cnaes = db.session.query(Cliente.cnae).filter(Cliente.cnae != None).distinct().all()

        return jsonify({
            "options": {
                "ufs": [uf[0] for uf in ufs if uf[0]],
                "atividades": [atividade[0] for atividade in atividades if atividade[0]],
                "destinacoes": [destinacao[0] for destinacao in destinacoes if destinacao[0]],
                "cnaes": [cnae[0] for cnae in cnaes if cnae[0]]
            }
        }), 200

    except Exception as e:
        print(f"Erro ao obter opções de filtro: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/municipios', methods=['GET'])
@jwt_required()
def obter_municipios():
    """
    Obtém os municípios para um estado específico
    """
    try:
        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        uf = request.args.get('uf')

        print(f"Obtendo municípios para UF: {uf}, empresa_id: {empresa_id}")

        if not uf:
            return jsonify({"message": "UF é obrigatório"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = Cliente.query.filter(Cliente.uf == uf)

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os clientes
            print("Usuário é admin, vendo todos os clientes")
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem clientes do seu escritório
            print(f"Usuário é do escritório {usuario.escritorio_id}, filtrando por escritório")
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas clientes das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            print(f"Usuário comum, empresas permitidas: {empresas_permitidas}")
            query = query.filter(Cliente.empresa_id.in_(empresas_permitidas))

        # Obter valores distintos para municípios
        municipios = query.with_entities(Cliente.municipio).filter(Cliente.municipio != None).distinct().all()

        result = [municipio[0] for municipio in municipios if municipio[0]]
        print(f"Encontrados {len(result)} municípios para UF {uf}: {result}")

        return jsonify({
            "municipios": result
        }), 200

    except Exception as e:
        print(f"Erro ao obter municípios: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/bulk-update', methods=['PUT'])
@jwt_required()
def atualizar_clientes_em_massa():
    """
    Atualiza múltiplos clientes de uma vez
    """
    try:
        data = request.get_json()

        # Verificar se os campos obrigatórios estão presentes
        if not data.get('cliente_ids') or not isinstance(data.get('cliente_ids'), list):
            return jsonify({"message": "Lista de IDs de clientes é obrigatória"}), 400

        # Verificar se pelo menos um campo para atualização foi fornecido
        if not data.get('atividade') and not data.get('destinacao'):
            return jsonify({"message": "Pelo menos um campo para atualização deve ser fornecido"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Obter os clientes a serem atualizados
        clientes = Cliente.query.filter(Cliente.id.in_(data.get('cliente_ids'))).all()

        if not clientes:
            return jsonify({"message": "Nenhum cliente encontrado com os IDs fornecidos"}), 404

        # Verificar permissões para cada cliente
        for cliente in clientes:
            if not (usuario.is_admin or usuario.tipo_usuario == 'admin' or
                    (usuario.tipo_usuario == 'escritorio' and cliente.escritorio_id == usuario.escritorio_id) or
                    (usuario.empresas_permitidas and cliente.empresa_id in usuario.empresas_permitidas)):
                return jsonify({"message": f"Você não tem permissão para atualizar o cliente {cliente.id}"}), 403

        # Atualizar os clientes
        for cliente in clientes:
            if data.get('atividade'):
                cliente.atividade = data.get('atividade')
            if data.get('destinacao'):
                cliente.destinacao = data.get('destinacao')

        db.session.commit()

        return jsonify({
            "message": f"{len(clientes)} cliente(s) atualizado(s) com sucesso"
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao atualizar clientes em massa: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@cliente_bp.route('/api/clientes/<int:cliente_id>', methods=['DELETE'])
@jwt_required()
def excluir_cliente(cliente_id):
    """
    Exclui um cliente
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar o cliente
        cliente = db.session.get(Cliente, cliente_id)

        if not cliente:
            return jsonify({"message": "Cliente não encontrado"}), 404

        # Verificar permissões para excluir o cliente
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem excluir qualquer cliente
            pass
        elif usuario.tipo_usuario == 'escritorio' and cliente.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem excluir clientes do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and cliente.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem excluir clientes das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para excluir este cliente"}), 403

        # Excluir o cliente
        db.session.delete(cliente)
        db.session.commit()

        return jsonify({
            "message": "Cliente excluído com sucesso"
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"Erro ao excluir cliente: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500