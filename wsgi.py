"""
Arquivo de entrada para produção usando Waitress.
Execute com: waitress-serve --port=5000 wsgi:app
"""
import os
import sys

# Obtém o diretório base do projeto
BASE_DIR = os.path.abspath(os.path.dirname(__file__))

# Adiciona o diretório back ao path
sys.path.insert(0, os.path.join(BASE_DIR, 'back'))
sys.path.insert(0, BASE_DIR)

# Importa a aplicação
from back.app import create_app

# Cria a aplicação
app, socketio = create_app()

# Configura o socketio para usar o Waitress
app.config['SOCKETIO_ASYNC_MODE'] = 'threading'

# Configurações de produção
app.config.update(
    ENV='production',
    DEBUG=False,
    TESTING=False
)

# Configura o logger
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('waitress')
logger.setLevel(logging.INFO)

def run():
    """Função para rodar a aplicação com Waitress"""
    from waitress import serve
    
    # Configuração do servidor
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', '5000'))
    
    logger.info(f"Iniciando servidor Waitress em {host}:{port}")
    
    # Inicia o servidor
    serve(
        app,
        host=host,
        port=port,
        threads=4,  # Ajuste conforme necessário
        url_scheme='https'  # Se estiver usando HTTPS
    )

if __name__ == '__main__':
    run()
