from models import db, Empresa, Cliente, Produto, Tributo, ImportacaoXML, NotaFiscalItem
from utils import XMLProcessor
from utils.api_client import fetch_cnpj_data
from services.cenario_service import CenarioService
from datetime import datetime
import traceback
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class XMLImportService:
    """
    Serviço para importação de arquivos XML de notas fiscais
    """

    def __init__(self, empresa_id, escritorio_id, usuario_id):
        """
        Inicializa o serviço de importação

        Args:
            empresa_id (int): ID da empresa
            escritorio_id (int): ID do escritório
            usuario_id (int): ID do usuário que está realizando a importação
        """
        self.empresa_id = empresa_id
        self.escritorio_id = escritorio_id
        self.usuario_id = usuario_id

    def import_xml(self, xml_content, filename):
        """
        Importa um arquivo XML

        Args:
            xml_content (str): Conteúdo do arquivo XML
            filename (str): Nome do arquivo

        Returns:
            dict: Resultado da importação
        """
        importacao = None
        try:
            # Processar o XML
            processor = XMLProcessor(xml_content)

            # Obter informações do XML
            info_nfe = processor.get_info_nfe()
            emitente = processor.get_emitente()
            destinatario = processor.get_destinatario()
            produtos = processor.get_produtos()

            # Verificar se a empresa existe
            empresa = Empresa.query.get(self.empresa_id)
            if not empresa:
                # Criar registro de importação com erro
                importacao = self._create_error_import_record(filename, 'Empresa não encontrada')
                return {
                    'success': False,
                    'message': 'Empresa não encontrada',
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Verificar se o CNPJ do emitente corresponde ao CNPJ da empresa
            if emitente.get('cnpj') != empresa.cnpj:
                # Criar registro de importação com erro
                error_msg = f'O CNPJ do emitente ({emitente.get("cnpj")}) não corresponde ao CNPJ da empresa ({empresa.cnpj})'
                importacao = self._create_error_import_record(filename, error_msg)
                return {
                    'success': False,
                    'message': error_msg,
                    'importacao': importacao.to_dict() if importacao else None
                }

            # Processar o destinatário (cliente)
            cliente = self._process_cliente(destinatario)

            # Processar os produtos e tributos
            produtos_processados = []
            tributos_processados = []

            for produto_data in produtos:
                produto = self._process_produto(produto_data)
                tributo = self._process_tributo(produto_data, cliente.id, produto.id, info_nfe)

                produtos_processados.append(produto)
                tributos_processados.append(tributo)

            # Criar registro de importação com sucesso APENAS após processamento bem-sucedido
            importacao = ImportacaoXML(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                chave_nf=info_nfe.get('chave'),
                numero_nf=info_nfe.get('numero'),
                data_emissao=info_nfe.get('data_emissao'),
                cnpj_emitente=emitente.get('cnpj'),
                razao_social_emitente=emitente.get('nome'),
                status='concluido'
            )

            db.session.add(importacao)
            db.session.commit()

            # Preparar resposta com verificação de objetos None
            cliente_dict = cliente.to_dict() if cliente else None
            produtos_dict = []
            tributos_dict = []

            for produto in produtos_processados:
                if produto:
                    produtos_dict.append(produto.to_dict())

            for tributo in tributos_processados:
                if tributo:
                    tributos_dict.append(tributo.to_dict())

            return {
                'success': True,
                'message': 'Importação realizada com sucesso',
                'importacao_id': importacao.id,
                'cliente': cliente_dict,
                'produtos': produtos_dict,
                'tributos': tributos_dict
            }

        except Exception as e:
            db.session.rollback()
            error_msg = str(e)
            stack_trace = traceback.format_exc()

            logger.error(f"Erro ao importar XML {filename}: {error_msg}")
            logger.error(f"Stack trace: {stack_trace}")

            # Criar registro de importação com erro apenas se não foi criado antes
            if not importacao:
                importacao = self._create_error_import_record(filename, error_msg, stack_trace)

            return {
                'success': False,
                'message': f'Erro ao importar XML: {error_msg}',
                'error': error_msg,
                'stack_trace': stack_trace,
                'importacao': importacao.to_dict() if importacao else None
            }

    def _create_error_import_record(self, filename, error_msg, stack_trace=None):
        """
        Cria um registro de importação com erro

        Args:
            filename (str): Nome do arquivo
            error_msg (str): Mensagem de erro
            stack_trace (str): Stack trace do erro (opcional)

        Returns:
            ImportacaoXML: Objeto da importação criado ou None se falhar
        """
        try:
            full_message = error_msg
            if stack_trace:
                full_message = f"{error_msg}\n\n{stack_trace}"

            importacao = ImportacaoXML(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                usuario_id=self.usuario_id,
                arquivo_nome=filename,
                status='erro',
                mensagem=full_message
            )

            db.session.add(importacao)
            db.session.commit()
            return importacao

        except Exception as inner_e:
            logger.error(f"Erro ao registrar importação com erro: {str(inner_e)}")
            return None

    def _process_cliente(self, destinatario):
        """
        Processa o destinatário e cria/atualiza o cliente

        Args:
            destinatario (dict): Dados do destinatário

        Returns:
            Cliente: Objeto do cliente
        """
        # Obter CNPJ ou CPF do destinatário
        cnpj = destinatario.get('cnpj')
        cpf = destinatario.get('cpf')

        # Se for pessoa física (CPF), usar o CPF como CNPJ para manter compatibilidade
        # com o modelo de dados atual
        documento = cnpj if cnpj else cpf if cpf else None

        # Registrar o tipo de documento para diagnóstico
        tipo_documento = "CNPJ" if cnpj else "CPF" if cpf else "Nenhum"
        logger.info(f"Processando cliente: {destinatario.get('nome')} - {tipo_documento}: {documento}")

        if not documento:
            raise ValueError("Destinatário sem CNPJ ou CPF. Não é possível processar o cliente.")

        # Verificar se o cliente já existe
        cliente = Cliente.query.filter_by(
            empresa_id=self.empresa_id,
            cnpj=documento
        ).first()

        # Garantir que a inscrição estadual seja uma string
        inscricao_estadual = destinatario.get('ie')
        if inscricao_estadual is not None and not isinstance(inscricao_estadual, str):
            # Se não for uma string, converter para string ou definir como None
            try:
                inscricao_estadual = str(inscricao_estadual)
            except:
                inscricao_estadual = None

        # Determinar atividade com base no indIEDest
        ind_ie_dest = destinatario.get('ind_ie_dest')
        atividade = None
        if ind_ie_dest == '9':
            atividade = 'Não Contribuinte'

        # Determinar destinação com base no indFinal
        ind_final = destinatario.get('ind_final')
        destinacao = None
        if ind_final == '1':
            destinacao = 'Uso e Consumo'

        # Variáveis para armazenar dados da API
        cnae = None
        simples_nacional = False

        # Se for um novo cliente com CNPJ (não CPF), buscar dados adicionais na API
        if not cliente and cnpj and not cpf:
            logger.info(f"Cliente não encontrado no banco de dados. Buscando dados do CNPJ {cnpj} na API.")
            try:
                # Buscar dados do CNPJ na API
                api_data = fetch_cnpj_data(cnpj)

                if api_data:
                    # Extrair CNAE, atividade, destinação e status do Simples Nacional
                    cnae = api_data.get('cnae')
                    simples_nacional = api_data.get('simples_nacional', False)

                    # Verificar se a natureza jurídica é 'Produtor Rural' e tem prioridade
                    natureza_juridica = api_data.get('natureza_juridica', {})
                    descricao_natureza_juridica = natureza_juridica.get('descricao', '').lower() if natureza_juridica else ''
                    
                    if 'produtor rural' in descricao_natureza_juridica:
                        # Se a natureza jurídica for 'Produtor Rural', definir a atividade como 'Produtor Rural'
                        # independentemente do CNAE
                        atividade = 'Produtor Rural'
                        logger.info(f"Natureza jurídica identificada como 'Produtor Rural'. Atividade definida como: {atividade}")
                    # Se não for 'Produtor Rural', verificar se já tem atividade definida
                    elif not atividade and api_data.get('atividade'):
                        # Se não tiver atividade definida pelo indIEDest, usar a da API
                        atividade = api_data.get('atividade')
                        logger.info(f"Usando atividade da API: {atividade}")

                    # Se não tiver destinação definida pelo indFinal, usar a da API
                    if not destinacao and api_data.get('destinacao'):
                        destinacao = api_data.get('destinacao')
                        logger.info(f"Usando destinação da API: {destinacao}")

                    
                    logger.info(f"Dados obtidos da API: CNAE={cnae}, Divisão={api_data.get('divisao_cnae')}, "
                                f"Atividade={atividade}, Destinação={destinacao}, "
                                f"Natureza Jurídica={descricao_natureza_juridica}, "
                                f"Simples Nacional={simples_nacional}")
                else:
                    logger.warning(f"Não foi possível obter dados do CNPJ {cnpj} na API.")
            except Exception as e:
                logger.error(f"Erro ao buscar dados do CNPJ {cnpj} na API: {str(e)}")

        if cliente:
            # Cliente já existe, atualizar informações
            cliente.razao_social = destinatario.get('nome', cliente.razao_social)
            cliente.inscricao_estadual = inscricao_estadual if inscricao_estadual is not None else cliente.inscricao_estadual
            cliente.logradouro = destinatario.get('logradouro', cliente.logradouro)
            cliente.numero = destinatario.get('numero', cliente.numero)
            cliente.bairro = destinatario.get('bairro', cliente.bairro)
            cliente.municipio = destinatario.get('municipio', cliente.municipio)
            cliente.uf = destinatario.get('uf', cliente.uf)
            cliente.cep = destinatario.get('cep', cliente.cep)
            cliente.pais = destinatario.get('pais', cliente.pais)
            cliente.codigo_pais = destinatario.get('codigo_pais', cliente.codigo_pais)

            # Atualizar novos campos apenas se não estiverem definidos ou se tiverem valores específicos
            if ind_ie_dest:
                cliente.ind_ie_dest = ind_ie_dest
            if ind_final:
                cliente.ind_final = ind_final
            if atividade and not cliente.atividade:
                cliente.atividade = atividade
            if destinacao and not cliente.destinacao:
                cliente.destinacao = destinacao
        else:
            # Criar novo cliente
            cliente = Cliente(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cnpj=documento,  # Usar o documento (CNPJ ou CPF)
                razao_social=destinatario.get('nome', ''),
                inscricao_estadual=inscricao_estadual,
                logradouro=destinatario.get('logradouro'),
                numero=destinatario.get('numero'),
                bairro=destinatario.get('bairro'),
                municipio=destinatario.get('municipio'),
                uf=destinatario.get('uf'),
                cep=destinatario.get('cep'),
                pais=destinatario.get('pais'),
                codigo_pais=destinatario.get('codigo_pais'),
                ind_ie_dest=ind_ie_dest,
                ind_final=ind_final,
                atividade=atividade,
                destinacao=destinacao,
                cnae=cnae,  # Adicionar CNAE obtido da API
                simples_nacional=simples_nacional,  # Adicionar status do Simples Nacional
                status='novo',
                data_cadastro=datetime.now()
            )
            db.session.add(cliente)
            db.session.flush()  # Obter o ID sem commit

            logger.info(f"Novo cliente criado: ID={cliente.id}, CNPJ/CPF={documento}, CNAE={cnae}, Simples Nacional={simples_nacional}")

        return cliente

    def _process_produto(self, produto_data):
        """
        Processa o produto e verifica se já existe um produto com o mesmo código
        Se existir, usa o produto existente, caso contrário cria um novo

        Args:
            produto_data (dict): Dados do produto

        Returns:
            Produto: Objeto do produto
        """
        # Verificar se o CFOP está na lista para definir o tipo SPED
        cfop = str(produto_data.get('cfop', '')).strip()
        cfops_produto_acabado = ['5101', '6101', '5401', '6401', '6107-8', '61078']

        # Verificar se já existe um produto com o mesmo código
        produto = Produto.query.filter_by(
            empresa_id=self.empresa_id,
            codigo=produto_data.get('codigo', ''),
        ).first()

        if produto:
            logger.info(f"Usando produto existente: ID={produto.id}, {produto.codigo} - {produto.descricao}")
            return produto

        # Se não existir, criar um novo produto
        try:
            produto = Produto(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                codigo=produto_data.get('codigo', ''),
                descricao=produto_data.get('descricao', ''),
                # Não armazenar NCM e CFOP na tabela de produtos, apenas na tabela nota_fiscal_item
                unidade_comercial=produto_data.get('unidade_comercial'),
                unidade_tributavel=produto_data.get('unidade_tributavel'),
                codigo_ean=produto_data.get('codigo_ean'),
                codigo_ean_tributavel=produto_data.get('codigo_ean_tributavel'),
                unidade_tributaria=produto_data.get('unidade_tributaria'),
                tipo_sped='Produto Acabado' if cfop in cfops_produto_acabado else None,
                cest=produto_data.get('cest'),  # Adicionar o campo CEST
                status='novo'
            )
            db.session.add(produto)
            db.session.flush()  # Obter o ID sem commit

            logger.info(f"Criado novo produto: ID={produto.id}, {produto.codigo} - {produto.descricao}")
        except Exception as e:
            # Se ocorrer um erro, tentar encontrar o produto existente apenas pelo código
            db.session.rollback()
            logger.warning(f"Erro ao criar produto: {str(e)}. Tentando encontrar produto existente pelo código.")

            # Buscar produto existente com o mesmo código e empresa
            produto = Produto.query.filter_by(
                empresa_id=self.empresa_id,
                codigo=produto_data.get('codigo', '')
            ).first()

            if not produto:
                # Se não encontrar, gerar um código único adicionando um timestamp
                import time
                unique_code = f"{produto_data.get('codigo', '')}-{int(time.time())}"
                logger.info(f"Criando produto com código único: {unique_code}")

                produto = Produto(
                    empresa_id=self.empresa_id,
                    escritorio_id=self.escritorio_id,
                    codigo=unique_code,
                    descricao=produto_data.get('descricao', ''),
                    # Não armazenar NCM e CFOP na tabela de produtos, apenas na tabela nota_fiscal_item
                    unidade_comercial=produto_data.get('unidade_comercial'),
                    unidade_tributavel=produto_data.get('unidade_tributavel'),
                    codigo_ean=produto_data.get('codigo_ean'),
                    codigo_ean_tributavel=produto_data.get('codigo_ean_tributavel'),
                    unidade_tributaria=produto_data.get('unidade_tributaria'),
                    tipo_sped='Produto Acabado' if cfop in cfops_produto_acabado else None,
                    cest=produto_data.get('cest'),  # Adicionar o campo CEST
                    status='novo'
                )
                db.session.add(produto)
                db.session.flush()  # Obter o ID sem commit
                logger.info(f"Criado novo produto com código único: ID={produto.id}, {produto.codigo} - {produto.descricao}")
            else:
                logger.info(f"Usando produto existente pelo código: ID={produto.id}, {produto.codigo} - {produto.descricao}")

        return produto

    def _process_tributo(self, produto_data, cliente_id, produto_id, info_nfe):
        """
        Processa os tributos do produto

        Args:
            produto_data (dict): Dados do produto com tributos
            cliente_id (int): ID do cliente
            produto_id (int): ID do produto
            info_nfe (dict): Informações da NFe

        Returns:
            Tributo: Objeto do tributo
        """
        # Inicializar o serviço de cenários
        cenario_service = CenarioService(self.empresa_id, self.escritorio_id)

        # Verificar se já existe um item de nota fiscal para este produto/cliente/data/NF
        nota_fiscal_item = NotaFiscalItem.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            data_emissao=info_nfe.get('data_emissao'),
            numero_nf=info_nfe.get('numero')
        ).first()

        # Se não existir, criar um novo item de nota fiscal
        if not nota_fiscal_item:
            # Criar novo item de nota fiscal
            nota_fiscal_item = NotaFiscalItem(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                data_emissao=info_nfe.get('data_emissao'),
                numero_nf=info_nfe.get('numero'),
                chave_nf=info_nfe.get('chave'),
                cfop=produto_data.get('cfop'),
                ncm=produto_data.get('ncm'),
                unidade_comercial=produto_data.get('unidade_comercial'),
                quantidade=produto_data.get('quantidade'),
                valor_unitario=produto_data.get('valor_unitario'),
                valor_total=produto_data.get('valor_total')
            )
            db.session.add(nota_fiscal_item)
            db.session.flush()  # Obter o ID sem commit
            logger.info(f"Criado novo item de nota fiscal para produto_id={produto_id}, cliente_id={cliente_id}, NF={info_nfe.get('numero')}")

        # Verificar se já existe um tributo para este produto/cliente/data/NF
        tributo = Tributo.query.filter_by(
            empresa_id=self.empresa_id,
            cliente_id=cliente_id,
            produto_id=produto_id,
            data_emissao=info_nfe.get('data_emissao'),
            numero_nf=info_nfe.get('numero')
        ).first()

        # Se não existir, criar um novo tributo
        if not tributo:
            # Criar novo tributo
            tributo = Tributo(
                empresa_id=self.empresa_id,
                escritorio_id=self.escritorio_id,
                cliente_id=cliente_id,
                produto_id=produto_id,
                data_emissao=info_nfe.get('data_emissao'),
                data_saida=info_nfe.get('data_saida'),
                numero_nf=info_nfe.get('numero'),
                chave_nf=info_nfe.get('chave'),
                tipo_operacao=info_nfe.get('tipo_operacao'),  # 0=entrada, 1=saída
                status='novo',
                auditoria_status='pendente',  # Definir explicitamente como pendente
                nota_fiscal_item_id=nota_fiscal_item.id  # Vincular ao item de nota fiscal
            )
            db.session.add(tributo)
            logger.info(f"Criado novo tributo para produto_id={produto_id}, cliente_id={cliente_id}, NF={info_nfe.get('numero')}")

        # Atualizar informações de tributos
        # ICMS
        tributo.icms_origem = produto_data.get('icms_origem')
        tributo.icms_cst = produto_data.get('icms_cst')
        tributo.icms_mod_bc = produto_data.get('icms_mod_bc')
        tributo.icms_p_red_bc = produto_data.get('icms_p_red_bc')
        tributo.icms_vbc = produto_data.get('icms_vbc')
        tributo.icms_aliquota = produto_data.get('icms_aliquota')
        tributo.icms_valor = produto_data.get('icms_valor')
        # Novos campos ICMS
        tributo.icms_v_op = produto_data.get('icms_v_op')
        tributo.icms_p_dif = produto_data.get('icms_p_dif')
        tributo.icms_v_dif = produto_data.get('icms_v_dif')

        # ICMS-ST
        tributo.icms_st_mod_bc = produto_data.get('icms_st_mod_bc')
        tributo.icms_st_p_mva = produto_data.get('icms_st_p_mva')
        tributo.icms_st_vbc = produto_data.get('icms_st_vbc')
        tributo.icms_st_aliquota = produto_data.get('icms_st_aliquota')
        tributo.icms_st_valor = produto_data.get('icms_st_valor')

        # IPI
        tributo.ipi_cst = produto_data.get('ipi_cst')
        tributo.ipi_vbc = produto_data.get('ipi_vbc')
        tributo.ipi_aliquota = produto_data.get('ipi_aliquota')
        tributo.ipi_valor = produto_data.get('ipi_valor')
        tributo.ipi_codigo_enquadramento = produto_data.get('ipi_codigo_enquadramento')
        tributo.ipi_ex = produto_data.get('extipi')  # Campo EXTIPI do produto

        # PIS
        tributo.pis_cst = produto_data.get('pis_cst')
        tributo.pis_vbc = produto_data.get('pis_vbc')
        tributo.pis_aliquota = produto_data.get('pis_aliquota')
        tributo.pis_valor = produto_data.get('pis_valor')
        tributo.pis_p_red_bc = produto_data.get('pis_p_red_bc')  # Novo campo para percentual de redução

        # COFINS
        tributo.cofins_cst = produto_data.get('cofins_cst')
        tributo.cofins_vbc = produto_data.get('cofins_vbc')
        tributo.cofins_aliquota = produto_data.get('cofins_aliquota')
        tributo.cofins_valor = produto_data.get('cofins_valor')
        tributo.cofins_p_red_bc = produto_data.get('cofins_p_red_bc')  # Novo campo para percentual de redução

        # DIFAL
        tributo.difal_vbc = produto_data.get('difal_vbc')
        tributo.difal_p_fcp_uf_dest = produto_data.get('difal_p_fcp_uf_dest')
        tributo.difal_p_icms_uf_dest = produto_data.get('difal_p_icms_uf_dest')
        tributo.difal_p_icms_inter = produto_data.get('difal_p_icms_inter')
        tributo.difal_p_icms_inter_part = produto_data.get('difal_p_icms_inter_part')
        tributo.difal_v_fcp_uf_dest = produto_data.get('difal_v_fcp_uf_dest')
        tributo.difal_v_icms_uf_dest = produto_data.get('difal_v_icms_uf_dest')
        tributo.difal_v_icms_uf_remet = produto_data.get('difal_v_icms_uf_remet')

        # Valores do produto
        tributo.quantidade = produto_data.get('quantidade')
        tributo.valor_unitario = produto_data.get('valor_unitario')
        tributo.valor_total = produto_data.get('valor_total')
        tributo.valor_frete = produto_data.get('valor_frete')  # Novo campo para frete proporcional
        tributo.valor_desconto = produto_data.get('valor_desconto')  # Novo campo para desconto proporcional

        # Determinar a direção com base no tipo_operacao
        direcao = 'entrada' if tributo.tipo_operacao == '0' else 'saida'

        # Obter o NCM do item da nota fiscal
        ncm = nota_fiscal_item.ncm
        cfop = nota_fiscal_item.cfop

        # Criar cenários para cada tipo de tributo
        # ICMS
        if tributo.icms_valor is not None:
            icms_data = {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_dif': tributo.icms_p_dif,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_icms = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms', icms_data)
            # Armazenar o ID do cenário no tributo
            if cenario_icms:
                tributo.cenario_icms_id = cenario_icms.id

        # ICMS-ST
        if tributo.icms_st_valor is not None:
            icms_st_data = {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'icms_st_mod_bc': tributo.icms_st_mod_bc,
                'icms_st_aliquota': tributo.icms_st_aliquota,
                'icms_st_p_mva': tributo.icms_st_p_mva,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_icms_st = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'icms_st', icms_st_data)
            # Armazenar o ID do cenário no tributo
            if cenario_icms_st:
                tributo.cenario_icms_st_id = cenario_icms_st.id

        # IPI
        if tributo.ipi_valor is not None:
            ipi_data = {
                'cst': tributo.ipi_cst,
                'aliquota': tributo.ipi_aliquota,
                'ex': tributo.ipi_ex,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_ipi = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'ipi', ipi_data)
            # Armazenar o ID do cenário no tributo
            if cenario_ipi:
                tributo.cenario_ipi_id = cenario_ipi.id

        # PIS
        if tributo.pis_valor is not None:
            pis_data = {
                'cst': tributo.pis_cst,
                'aliquota': tributo.pis_aliquota,
                'p_red_bc': tributo.pis_p_red_bc,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_pis = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'pis', pis_data)
            # Armazenar o ID do cenário no tributo
            if cenario_pis:
                tributo.cenario_pis_id = cenario_pis.id

        # COFINS
        if tributo.cofins_valor is not None:
            cofins_data = {
                'cst': tributo.cofins_cst,
                'aliquota': tributo.cofins_aliquota,
                'p_red_bc': tributo.cofins_p_red_bc,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_cofins = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'cofins', cofins_data)
            # Armazenar o ID do cenário no tributo
            if cenario_cofins:
                tributo.cenario_cofins_id = cenario_cofins.id

        # DIFAL
        if tributo.difal_v_icms_uf_dest is not None:
            difal_data = {
                'origem': tributo.icms_origem,
                'cst': tributo.icms_cst,
                'mod_bc': tributo.icms_mod_bc,
                'p_red_bc': tributo.icms_p_red_bc,
                'aliquota': tributo.icms_aliquota,
                'p_fcp_uf_dest': tributo.difal_p_fcp_uf_dest,
                'p_icms_uf_dest': tributo.difal_p_icms_uf_dest,
                'p_icms_inter': tributo.difal_p_icms_inter,
                'p_icms_inter_part': tributo.difal_p_icms_inter_part,
                'direcao': direcao,
                'tipo_operacao': tributo.tipo_operacao,
                'cfop': cfop,  # Incluir CFOP nos dados do cenário
                'ncm': ncm     # Incluir NCM nos dados do cenário
            }
            cenario_difal = cenario_service.criar_cenario_importacao(cliente_id, produto_id, 'difal', difal_data)
            # Armazenar o ID do cenário no tributo
            if cenario_difal:
                tributo.cenario_difal_id = cenario_difal.id

        return tributo

