"""
Script para testar a conexão com o banco de dados PostgreSQL.
"""
import sys
import psycopg2
from psycopg2 import OperationalError

# Configurar a codificação padrão para UTF-8
if sys.version_info[0] < 3:
    reload(sys)
    sys.setdefaultencoding('utf-8')
else:
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

def test_connection(host, port, dbname, user, password):
    """Testa a conexão com o banco de dados PostgreSQL."""
    try:
        print(f"\nTentando conectar ao banco de dados...")
        print(f"Host: {host}")
        print(f"Porta: {port}")
        print(f"Banco: {dbname}")
        print(f"Usuário: {user}")
        
        # Tenta estabelecer a conexão com parâmetros codificados
        conn_params = {
            'host': str(host).encode('utf-8').decode('utf-8'),
            'port': str(port).encode('utf-8').decode('utf-8'),
            'dbname': str(dbname).encode('utf-8').decode('utf-8'),
            'user': str(user).encode('utf-8').decode('utf-8'),
            'password': str(password).encode('utf-8').decode('utf-8'),
            'client_encoding': 'utf8',
            'connect_timeout': 5
        }
        
        print("\nParâmetros de conexão:", conn_params)
        
        conn = psycopg2.connect(**conn_params)
        
        # Cria um cursor para executar consultas
        cursor = conn.cursor()
        
        # Executa uma consulta simples para verificar a conexão
        cursor.execute("SELECT version();")
        db_version = cursor.fetchone()
        
        print("\n✅ Conexão bem-sucedida!")
        print(f"Versão do PostgreSQL: {db_version[0]}")
        
        # Fecha a conexão
        cursor.close()
        conn.close()
        return True
        
    except OperationalError as e:
        print("\n❌ Falha na conexão com o banco de dados:")
        print(f"Tipo do erro: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        print(f"Detalhes: {e.pgerror if hasattr(e, 'pgerror') else 'N/A'}")
        return False
    except UnicodeDecodeError as ude:
        print("\n❌ Erro de decodificação de caracteres:")
        print(f"Tipo: {type(ude).__name__}")
        print(f"Mensagem: {str(ude)}")
        print("Dica: Verifique se todos os parâmetros de conexão estão corretos e se o servidor está configurado para usar UTF-8.")
        return False
    except Exception as e:
        print("\n❌ Ocorreu um erro inesperado:")
        print(f"Tipo: {type(e).__name__}")
        print(f"Mensagem: {str(e)}")
        if hasattr(e, 'args'):
            print("Argumentos do erro:")
            for i, arg in enumerate(e.args):
                print(f"  {i}: {arg}")
        return False

def main():
    print("=== Teste de Conexão com PostgreSQL ===")
    
    # Configurações de conexão
    host = input("Host [localhost]: ") or "localhost"
    port = input("Porta [5432]: ") or "5432"
    dbname = input("Nome do banco de dados [postgres]: ") or "postgres"
    user = input("Usuário [postgres]: ") or "postgres"
    password = input("Senha: ") or ""
    
    # Testa a conexão
    test_connection(host, port, dbname, user, password)

if __name__ == "__main__":
    main()
