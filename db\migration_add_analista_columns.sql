-- Migration to add analyst review columns to auditoria_resultado table
-- Execute this script manually in PgAdmin

-- Add columns for analyst review functionality
ALTER TABLE auditoria_resultado ADD COLUMN IF NOT EXISTS analista_visualizou BOOLEAN DEFAULT FALSE;
ALTER TABLE auditoria_resultado ADD COLUMN IF NOT EXISTS observacoes_analista TEXT;
ALTER TABLE auditoria_resultado ADD COLUMN IF NOT EXISTS data_visualizacao TIMESTAMP;
ALTER TABLE auditoria_resultado ADD COLUMN IF NOT EXISTS usuario_analista_id INTEGER REFERENCES usuario(id);

-- Add index for better performance on queries filtering by analyst review
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_analista_visualizou ON auditoria_resultado(analista_visualizou);
CREATE INDEX IF NOT EXISTS idx_auditoria_resultado_data_visualizacao ON auditoria_resultado(data_visualizacao);

-- Add comments to document the new columns
COMMENT ON COLUMN auditoria_resultado.analista_visualizou IS 'Indica se a inconsistência foi visualizada e analisada pelo analista';
COMMENT ON COLUMN auditoria_resultado.observacoes_analista IS 'Observações e comentários do analista sobre a inconsistência';
COMMENT ON COLUMN auditoria_resultado.data_visualizacao IS 'Data e hora em que o analista marcou a inconsistência como visualizada';
COMMENT ON COLUMN auditoria_resultado.usuario_analista_id IS 'ID do usuário analista que marcou a inconsistência como visualizada';
