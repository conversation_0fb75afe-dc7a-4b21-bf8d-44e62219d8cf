-- Migração para adicionar campos de contagem de notas conformes e inconsistentes
-- Data: 2024-12-19
-- Descrição: Adiciona campos notas_conformes e notas_inconsistentes à tabela auditoria_sumario

-- Adicionar as novas colunas
ALTER TABLE auditoria_sumario 
ADD COLUMN IF NOT EXISTS notas_conformes INTEGER DEFAULT 0 NOT NULL;

ALTER TABLE auditoria_sumario 
ADD COLUMN IF NOT EXISTS notas_inconsistentes INTEGER DEFAULT 0 NOT NULL;

-- Atualizar os valores existentes baseado nos dados atuais
-- Para registros existentes, calcular baseado na lógica atual
UPDATE auditoria_sumario 
SET 
    notas_inconsistentes = LEAST(total_inconsistente, total_notas),
    notas_conformes = total_notas - LEAST(total_inconsistente, total_notas)
WHERE notas_conformes = 0 AND notas_inconsistentes = 0;

-- Comentário sobre a lógica:
-- notas_inconsistentes = mínimo entre total_inconsistente (itens) e total_notas
-- notas_conformes = total_notas - notas_inconsistentes
-- <PERSON><PERSON> assume que uma nota pode ter múltiplos itens, mas se pelo menos um item
-- for inconsistente, a nota toda é considerada inconsistente
