/**
 * cenario_modal_enhanced.js - Auditoria Fiscal
 * Funções para gerenciar o modal de edição de cenários
 */

// Variáveis globais - usando namespace para evitar conflitos
window.cenarioModal = window.cenarioModal || {
  currentCenario: null,
  currentTipoTributo: null,
};

/**
 * Formata a vigência de um cenário
 * @param {string} dataInicio - Data de início da vigência (formato ISO: YYYY-MM-DD)
 * @param {string} dataFim - Data de fim da vigência (formato ISO: YYYY-MM-DD)
 * @returns {string} Texto formatado da vigência
 */
function formatVigencia(dataInicio, dataFim) {
  if (!dataInicio) {
    return '-';
  }

  // Converter data de YYYY-MM-DD para Date, ajustando para o fuso horário local
  const ajustarData = (dataStr) => {
    if (!dataStr) return null;
    // Adiciona o horário como meio-dia no fuso horário local para evitar problemas de timezone
    const [year, month, day] = dataStr.split('-').map(Number);
    return new Date(year, month - 1, day, 12, 0, 0); // Mês é 0-indexed no construtor Date
  };

  const inicio = ajustarData(dataInicio);
  const inicioFormatado = inicio.toLocaleDateString('pt-BR');

  if (!dataFim) {
    return `A partir de ${inicioFormatado}`;
  }

  const fim = ajustarData(dataFim);
  const fimFormatado = fim.toLocaleDateString('pt-BR');

  return `${inicioFormatado} até ${fimFormatado}`;
}

/**
 * Exibe o modal de edição de cenário
 * @param {Object} cenario - Dados do cenário
 */
function showCenarioModal(cenario) {
  console.log('Exibindo modal de cenário:', cenario);

  // Armazenar o cenário atual
  window.cenarioModal.currentCenario = cenario;
  window.cenarioModal.currentTipoTributo =
    window.cenariosDetalhes.currentTipoTributo;

  // Verificar se o modal já existe
  let modal = document.getElementById('cenario-modal');

  // Se não existir, criar o modal
  if (!modal) {
    modal = document.createElement('div');
    modal.id = 'cenario-modal';
    modal.className = 'modal fade';
    modal.tabIndex = -1;
    modal.setAttribute('aria-labelledby', 'cenario-modal-label');
    modal.setAttribute('aria-hidden', 'true');

    modal.innerHTML = `
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="cenario-modal-label">Detalhes do Cenário</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
          </div>
          <div class="modal-body">
            <ul class="nav nav-tabs" id="cenario-modal-tabs" role="tablist">
              <li class="nav-item" role="presentation">
                <button class="nav-link active" id="cliente-tab" data-bs-toggle="tab" data-bs-target="#cliente-content" type="button" role="tab" aria-controls="cliente-content" aria-selected="true">
                  Cliente
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="produto-tab" data-bs-toggle="tab" data-bs-target="#produto-content" type="button" role="tab" aria-controls="produto-content" aria-selected="false">
                  Produto
                </button>
              </li>
              <li class="nav-item" role="presentation">
                <button class="nav-link" id="tributo-tab" data-bs-toggle="tab" data-bs-target="#tributo-content" type="button" role="tab" aria-controls="tributo-content" aria-selected="false">
                  Tributo
                </button>
              </li>
            </ul>
            <div class="tab-content" id="cenario-modal-tabs-content">
              <div class="tab-pane fade show active" id="cliente-content" role="tabpanel" aria-labelledby="cliente-tab">
                <div id="cliente-details" class="mt-3">
                  <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Carregando...</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="produto-content" role="tabpanel" aria-labelledby="produto-tab">
                <div id="produto-details" class="mt-3">
                  <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Carregando...</span>
                    </div>
                  </div>
                </div>
              </div>
              <div class="tab-pane fade" id="tributo-content" role="tabpanel" aria-labelledby="tributo-tab">
                <div id="tributo-details" class="mt-3">
                  <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                      <span class="visually-hidden">Carregando...</span>
                    </div>
                  </div>
                </div>
                <div id="producao-comparison-container" class="mt-4"></div>
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fechar</button>
            <button type="button" class="btn btn-primary" id="save-cenario-btn">Salvar</button>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Configurar eventos para as abas
    setupCenarioModalTabs();
  }

  // Inicializar o modal Bootstrap
  const modalInstance = new bootstrap.Modal(modal);

  // Carregar os dados do cenário
  loadCenarioModalData(cenario);

  // Exibir o modal
  modalInstance.show();
}

/**
 * Configura eventos para as abas do modal
 */
function setupCenarioModalTabs() {
  const tabs = document.querySelectorAll('#cenario-modal-tabs .nav-link');

  tabs.forEach((tab) => {
    tab.addEventListener('click', function (event) {
      const tabId = event.target.id;

      // Carregar dados específicos para cada aba
      if (tabId === 'cliente-tab') {
        loadClienteDetails();
      } else if (tabId === 'produto-tab') {
        loadProdutoDetails();
      } else if (tabId === 'tributo-tab') {
        loadTributoDetails();

        // Se o cenário for inconsistente, carregar dados de comparação
        if (window.cenarioModal.currentCenario.status === 'inconsistente') {
          loadProducaoComparisonData(window.cenarioModal.currentCenario);
        }
      }
    });
  });
}

/**
 * Carrega os dados do cenário no modal
 * @param {Object} cenario - Dados do cenário
 */
function loadCenarioModalData(cenario) {
  // Carregar dados do cliente
  loadClienteDetails();

  // Configurar o botão de salvar
  document.getElementById('save-cenario-btn').onclick = function () {
    saveCenarioChanges();
  };
}

/**
 * Carrega os detalhes do cliente
 */
function loadClienteDetails() {
  const container = document.getElementById('cliente-details');
  if (!container) return;

  const cenario = window.cenarioModal.currentCenario;
  if (!cenario || !cenario.cliente) {
    container.innerHTML =
      '<div class="alert alert-warning">Dados do cliente não disponíveis.</div>';
    return;
  }

  const cliente = cenario.cliente;

  container.innerHTML = `
    <div class="card">
      <div class="card-header">
        <h5 class="card-title mb-0">Dados do Cliente</h5>
      </div>
      <div class="card-body">
        <form id="cliente-edit-form">
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="cliente-razao-social" class="form-label">Razão Social</label>
              <input type="text" class="form-control" id="cliente-razao-social" name="razao_social" value="${
                cliente.razao_social || cliente.nome || ''
              }">
            </div>
            <div class="col-md-6">
              <label for="cliente-cnpj" class="form-label">CNPJ/CPF</label>
              <input type="text" class="form-control" id="cliente-cnpj" name="cnpj" value="${
                cliente.cnpj || cliente.cpf || ''
              }" readonly>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="cliente-ie" class="form-label">Inscrição Estadual</label>
              <input type="text" class="form-control" id="cliente-ie" name="inscricao_estadual" value="${
                cliente.inscricao_estadual || ''
              }">
            </div>
            <div class="col-md-6">
              <label for="cliente-cnae" class="form-label">CNAE</label>
              <input type="text" class="form-control" id="cliente-cnae" name="cnae" value="${
                cliente.cnae || ''
              }">
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="cliente-cidade" class="form-label">Cidade</label>
              <input type="text" class="form-control" id="cliente-cidade" name="municipio" value="${
                cliente.municipio || ''
              }">
            </div>
            <div class="col-md-6">
              <label for="cliente-uf" class="form-label">UF</label>
              <input type="text" class="form-control" id="cliente-uf" name="uf" value="${
                cliente.uf || ''
              }">
            </div>
          </div>

          <div class="row mb-3">
            <div class="col-md-6">
              <label for="cliente-atividade" class="form-label">Atividade</label>
              <select class="form-select" id="cliente-atividade" name="atividade">
                <option value="">Selecione...</option>
                <option value="Indústria" ${
                  cliente.atividade === 'Indústria ou Equiparado'
                    ? 'selected'
                    : ''
                }>Indústria ou Equiparado</option>
                            <option value="Comércio Varejista" ${
                              cliente.atividade === 'Comércio Varejista'
                                ? 'selected'
                                : ''
                            }>Comércio Varejista</option>
                            <option value="Comércio Atacadista" ${
                              cliente.atividade === 'Comércio Atacadista'
                                ? 'selected'
                                : ''
                            }>Comércio Atacadista</option>
                            <option value="Distribuidor" ${
                              cliente.atividade === 'Distribuidor'
                                ? 'selected'
                                : ''
                            }>Distribuidor</option>
                            <option value="Produtor Rural" ${
                              cliente.atividade === 'Produtor Rural'
                                ? 'selected'
                                : ''
                            }>Produtor Rural</option>
                            <option value="Consumidor Final" ${
                              cliente.atividade === 'Consumidor Final'
                                ? 'selected'
                                : ''
                            }>Consumidor Final</option>
                            <option value="Órgão Público" ${
                              cliente.atividade === 'Órgão Público'
                                ? 'selected'
                                : ''
                            }>Órgão Público</option>
                            <option value="Serviço" ${
                              cliente.atividade === 'Serviço' ? 'selected' : ''
                            }>Serviço</option>
                            <option value="Não Contribuinte" ${
                              cliente.atividade === 'Não Contribuinte'
                                ? 'selected'
                                : ''
                            }>Não Contribuinte</option>
                          </select>
            </div>
            <div class="col-md-6">
              <label for="cliente-destinacao" class="form-label">Destinação</label>
              <select class="form-select" id="cliente-destinacao" name="destinacao">
                <option value="">Selecione...</option>
                <option value="Industrialização" ${
                  cliente.destinacao === 'Industrialização' ? 'selected' : ''
                }>Industrialização</option>
                <option value="Revenda" ${
                  cliente.destinacao === 'Revenda' ? 'selected' : ''
                }>Revenda</option>
                <option value="Ativo Imobilizado" ${
                  cliente.destinacao === 'Ativo Imobilizado' ? 'selected' : ''
                }>Ativo Imobilizado</option>
                <option value="Uso e Consumo" ${
                  cliente.destinacao === 'Uso e Consumo' ? 'selected' : ''
                }>Uso e Consumo</option>

              </select>
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="cliente-simples-nacional" name="simples_nacional" ${
                  cliente.simples_nacional ? 'checked' : ''
                }>
                <label class="form-check-label" for="cliente-simples-nacional">
                  Simples Nacional
                </label>
              </div>
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-primary" id="save-cliente-btn">Salvar Cliente</button>
          </div>
        </form>
      </div>
    </div>
  `;

  // Adicionar evento para o botão de salvar cliente
  document
    .getElementById('save-cliente-btn')
    .addEventListener('click', function () {
      saveClienteChanges(cliente.id);
    });
}

/**
 * Carrega os detalhes do produto
 */
function loadProdutoDetails() {
  const container = document.getElementById('produto-details');
  if (!container) return;

  const cenario = window.cenarioModal.currentCenario;
  if (!cenario || !cenario.produto) {
    container.innerHTML =
      '<div class="alert alert-warning">Dados do produto não disponíveis.</div>';
    return;
  }

  const produto = cenario.produto;

  // Criar o HTML para os dados do produto
  let produtoHTML = `
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="card-title mb-0">Dados do Produto</h5>
      </div>
      <div class="card-body">
        <form id="produto-edit-form">
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="produto-codigo" class="form-label">Código</label>
              <input type="text" class="form-control" id="produto-codigo" name="codigo" value="${
                produto.codigo || ''
              }" readonly>
            </div>
            <div class="col-md-6">
              <label for="produto-descricao" class="form-label">Descrição</label>
              <input type="text" class="form-control" id="produto-descricao" name="descricao" value="${
                produto.descricao || ''
              }">
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="produto-ncm" class="form-label">NCM</label>
              <input type="text" class="form-control" id="produto-ncm" name="ncm" value="${
                cenario.ncm || produto.ncm || ''
              }">
            </div>
            <div class="col-md-6">
              <label for="produto-cest" class="form-label">CEST</label>
              <input type="text" class="form-control" id="produto-cest" name="cest" value="${
                produto.cest || cenario.cest || ''
              }">
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="produto-ex" class="form-label">EX</label>
              <input type="text" class="form-control" id="produto-ex" name="ex" value="${
                produto.ex || cenario.ex || ''
              }">
            </div>
            <div class="col-md-6">
              <label for="produto-unidade" class="form-label">Unidade</label>
              <input type="text" class="form-control" id="produto-unidade" name="unidade" value="${
                produto.unidade_comercial || produto.unidade || ''
              }">
            </div>
          </div>
          <div class="row mb-3">
            <div class="col-md-6">
              <label for="produto-cfop" class="form-label">CFOP</label>
              <input type="text" class="form-control" id="produto-cfop" name="cfop" value="${
                produto.cfop || cenario.cfop || ''
              }">
            </div>
          </div>
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-primary" id="save-produto-btn">Salvar Produto</button>
          </div>
        </form>
      </div>
    </div>
  `;

  // Adicionar o formulário para edição dos dados do tributo
  produtoHTML += `
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0">Dados do Tributo</h5>
      </div>
      <div class="card-body">
        <form id="tributo-edit-form">
          ${getTributoFormFields(cenario)}
        </form>
      </div>
    </div>
  `;

  container.innerHTML = produtoHTML;

  // Adicionar evento para o botão de salvar produto
  document
    .getElementById('save-produto-btn')
    .addEventListener('click', function () {
      saveProdutoChanges(produto.id);
    });
}

/**
 * Carrega os detalhes do tributo
 */
function loadTributoDetails() {
  const container = document.getElementById('tributo-details');
  if (!container) return;

  const cenario = window.cenarioModal.currentCenario;
  if (!cenario) {
    container.innerHTML =
      '<div class="alert alert-warning">Dados do tributo não disponíveis.</div>';
    return;
  }

  // Obter HTML específico para o tipo de tributo
  const tributoHTML = getCenarioDetailsHTML(cenario);

  container.innerHTML = `
    <div class="card">
      <div class="card-body">
        <h5 class="card-title">Detalhes do Tributo</h5>
        <ul class="list-group list-group-flush">
          ${tributoHTML}
        </ul>
      </div>
    </div>
  `;
}

/**
 * Obtém os campos de formulário específicos para cada tipo de tributo
 * @param {Object} cenario - Dados do cenário
 * @returns {string} HTML com os campos de formulário
 */
function getTributoFormFields(cenario) {
  switch (window.cenarioModal.currentTipoTributo) {
    case 'icms':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="origem" name="origem" value="${
              cenario.origem || ''
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="p_red_bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_red_bc" name="p_red_bc" value="${
              cenario.p_red_bc || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="mod_bc" class="form-label">Modalidade BC</label>
            <input type="text" class="form-control" id="mod_bc" name="mod_bc" value="${
              cenario.mod_bc || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="p_dif" class="form-label">Percentual Diferimento (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_dif" name="p_dif" value="${
              cenario.p_dif || 0
            }">
          </div>
        </div>
      `;
    case 'icms_st':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="origem" name="origem" value="${
              cenario.origem || ''
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota ICMS (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="p_red_bc" class="form-label">Redução BC ICMS (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_red_bc" name="p_red_bc" value="${
              cenario.p_red_bc || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="mod_bc" class="form-label">Modalidade BC ICMS</label>
            <input type="text" class="form-control" id="mod_bc" name="mod_bc" value="${
              cenario.mod_bc || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="icms_st_p_mva" class="form-label">MVA (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms_st_p_mva" name="icms_st_p_mva" value="${
              cenario.icms_st_p_mva || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="icms_st_aliquota" class="form-label">Alíquota ICMS-ST (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms_st_aliquota" name="icms_st_aliquota" value="${
              cenario.icms_st_aliquota || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="icms_st_p_red_bc" class="form-label">Redução BC ICMS-ST (%)</label>
            <input type="number" step="0.01" class="form-control" id="icms_st_p_red_bc" name="icms_st_p_red_bc" value="${
              cenario.icms_st_p_red_bc || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="icms_st_mod_bc" class="form-label">Modalidade BC ICMS-ST</label>
            <input type="text" class="form-control" id="icms_st_mod_bc" name="icms_st_mod_bc" value="${
              cenario.icms_st_mod_bc || ''
            }">
          </div>
        </div>
      `;
    case 'ipi':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="codigo_enquadramento" class="form-label">Código Enquadramento</label>
            <input type="text" class="form-control" id="codigo_enquadramento" name="codigo_enquadramento" value="${
              cenario.codigo_enquadramento || ''
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="ex" class="form-label">EX</label>
            <input type="text" class="form-control" id="ex" name="ex" value="${
              cenario.ex || ''
            }">
          </div>
        </div>
      `;
    case 'pis':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="p_red_bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_red_bc" name="p_red_bc" value="${
              cenario.p_red_bc || 0
            }">
          </div>
        </div>
      `;
    case 'cofins':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="p_red_bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_red_bc" name="p_red_bc" value="${
              cenario.p_red_bc || 0
            }">
          </div>
        </div>
      `;
    case 'difal':
      return `
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="cst" class="form-label">CST</label>
            <input type="text" class="form-control" id="cst" name="cst" value="${
              cenario.cst || ''
            }">
          </div>
          <div class="col-md-6">
            <label for="origem" class="form-label">Origem</label>
            <input type="text" class="form-control" id="origem" name="origem" value="${
              cenario.origem || ''
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="aliquota" class="form-label">Alíquota (%)</label>
            <input type="number" step="0.01" class="form-control" id="aliquota" name="aliquota" value="${
              cenario.aliquota || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="p_red_bc" class="form-label">Redução BC (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_red_bc" name="p_red_bc" value="${
              cenario.p_red_bc || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="p_fcp_uf_dest" class="form-label">FCP UF Destino (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_fcp_uf_dest" name="p_fcp_uf_dest" value="${
              cenario.p_fcp_uf_dest || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="p_icms_uf_dest" class="form-label">ICMS UF Destino (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_icms_uf_dest" name="p_icms_uf_dest" value="${
              cenario.p_icms_uf_dest || 0
            }">
          </div>
        </div>
        <div class="row mb-3">
          <div class="col-md-6">
            <label for="p_icms_inter" class="form-label">ICMS Interestadual (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_icms_inter" name="p_icms_inter" value="${
              cenario.p_icms_inter || 0
            }">
          </div>
          <div class="col-md-6">
            <label for="p_icms_inter_part" class="form-label">Percentual Partilha (%)</label>
            <input type="number" step="0.01" class="form-control" id="p_icms_inter_part" name="p_icms_inter_part" value="${
              cenario.p_icms_inter_part || 0
            }">
          </div>
        </div>
      `;
    default:
      return `
        <div class="alert alert-warning">
          Não há campos específicos para o tipo de tributo ${window.cenarioModal.currentTipoTributo.toUpperCase()}.
        </div>
      `;
  }
}

/**
 * Salva as alterações feitas no cenário
 */
function saveCenarioChanges() {
  const cenario = window.cenarioModal.currentCenario;
  const tipoTributo = window.cenarioModal.currentTipoTributo;

  // Obter os valores do formulário
  const formData = {};
  const form = document.getElementById('tributo-edit-form');
  if (form) {
    const inputs = form.querySelectorAll('input');
    inputs.forEach((input) => {
      // Converter valores numéricos
      if (input.type === 'number') {
        formData[input.name] = parseFloat(input.value) || 0;
      } else {
        formData[input.name] = input.value;
      }
    });
  }

  // Adicionar dados necessários
  formData.id = cenario.id;
  formData.empresa_id = cenario.empresa_id;
  formData.escritorio_id = cenario.escritorio_id;

  // Fazer requisição para atualizar o cenário
  fetch(`/api/cenarios/${tipoTributo}/${cenario.id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify(formData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        let message = 'Cenário atualizado com sucesso!';

        // Adicionar informação sobre recálculo se foi realizado
        if (data.recalculo_realizado) {
          message += `\n\nOs valores de ${data.tipo_tributo_recalculado.toUpperCase()} foram recalculados automaticamente devido às alterações em campos críticos.`;
        }

        alert(message);

        // Recarregar os dados
        loadCenarioData(window.cenariosDetalhes.currentStatus);

        // Fechar o modal
        const modal = bootstrap.Modal.getInstance(
          document.getElementById('cenario-modal'),
        );
        if (modal) {
          modal.hide();
        }
      } else {
        alert(`Erro ao atualizar cenário: ${data.message}`);
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar cenário:', error);
      alert(`Erro ao atualizar cenário: ${error.message}`);
    });
}

/**
 * Obtém o HTML com os detalhes específicos do cenário
 * @param {Object} cenario - Dados do cenário
 * @returns {string} HTML com os detalhes do cenário
 */
function getCenarioDetailsHTML(cenario) {
  // Adicionar o status do cenário
  const statusHTML = `
    <li class="list-group-item list-group-item-info">
      <strong>Status:</strong> ${cenario.status || 'N/A'}
    </li>
    <li class="list-group-item list-group-item-info">
      <strong>Ativo:</strong> ${cenario.ativo ? 'Sim' : 'Não'}
    </li>
    <li class="list-group-item list-group-item-info">
      <strong>Vigência:</strong> ${formatVigencia(
        cenario.data_inicio_vigencia,
        cenario.data_fim_vigencia,
      )}
    </li>
  `;

  // Adicionar dados do cliente
  const clienteHTML = cenario.cliente
    ? `
    <li class="list-group-item list-group-item-secondary">
      <strong>Cliente:</strong> ${
        cenario.cliente.razao_social || cenario.cliente.nome || 'N/A'
      }
    </li>
    <li class="list-group-item list-group-item-secondary">
      <strong>Simples Nacional:</strong> ${
        cenario.cliente.simples_nacional ? 'Sim' : 'Não'
      }
    </li>
    <li class="list-group-item list-group-item-secondary">
      <strong>Atividade:</strong> ${cenario.cliente.atividade || 'N/A'}
    </li>
    <li class="list-group-item list-group-item-secondary">
      <strong>Destinação:</strong> ${cenario.cliente.destinacao || 'N/A'}
    </li>
    <li class="list-group-item list-group-item-secondary">
      <strong>Estado:</strong> ${cenario.cliente.uf || 'N/A'}
    </li>
  `
    : '';

  switch (window.cenarioModal.currentTipoTributo) {
    case 'icms':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          cenario.cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Origem:</strong> ${
          cenario.origem || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${
          cenario.aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
        <li class="list-group-item"><strong>Percentual ICMS Diferido:</strong> ${
          cenario.p_dif || '0'
        }%</li>
      `;
    case 'icms_st':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          cenario.cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Origem:</strong> ${
          cenario.origem || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Alíquota ICMS:</strong> ${
          cenario.aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC ICMS:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
        <li class="list-group-item"><strong>MVA (%):</strong> ${
          cenario.icms_st_p_mva || '0'
        }%</li>
        <li class="list-group-item"><strong>Alíquota ICMS-ST:</strong> ${
          cenario.icms_st_aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC ICMS-ST:</strong> ${
          cenario.icms_st_p_red_bc || '0'
        }%</li>
      `;
    case 'ipi':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          cenario.cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Código Enquadramento:</strong> ${
          cenario.codigo_enquadramento || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${
          cenario.aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
      `;
    case 'pis':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          cenario.cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${
          cenario.aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
      `;
    case 'cofins':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>CST:</strong> ${
          cenario.cst || 'N/A'
        }</li>
        <li class="list-group-item"><strong>Alíquota:</strong> ${
          cenario.aliquota || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
      `;
    case 'difal':
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>FCP UF Destino (%):</strong> ${
          cenario.p_fcp_uf_dest || '0'
        }%</li>
        <li class="list-group-item"><strong>ICMS UF Destino (%):</strong> ${
          cenario.p_icms_uf_dest || '0'
        }%</li>
        <li class="list-group-item"><strong>ICMS Interestadual (%):</strong> ${
          cenario.p_icms_inter || '0'
        }%</li>
        <li class="list-group-item"><strong>Percentual Partilha (%):</strong> ${
          cenario.p_icms_inter_part || '0'
        }%</li>
        <li class="list-group-item"><strong>Redução BC:</strong> ${
          cenario.p_red_bc || '0'
        }%</li>
      `;
    default:
      return `
        ${statusHTML}
        ${clienteHTML}
        <li class="list-group-item"><strong>Tipo:</strong> ${window.cenarioModal.currentTipoTributo.toUpperCase()}</li>
      `;
  }
}

/**
 * Salva as alterações feitas no cliente
 * @param {number} clienteId - ID do cliente
 */
function saveClienteChanges(clienteId) {
  // Obter os valores do formulário
  const formData = {};
  const form = document.getElementById('cliente-edit-form');
  if (form) {
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach((input) => {
      if (input.type === 'checkbox') {
        formData[input.name] = input.checked;
      } else {
        formData[input.name] = input.value;
      }
    });
  }

  // Adicionar ID do cliente
  formData.id = clienteId;

  // Fazer requisição para atualizar o cliente
  fetch(`/api/clientes/${clienteId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify(formData),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert('Cliente atualizado com sucesso!');

        // Atualizar os dados do cliente no cenário atual
        window.cenarioModal.currentCenario.cliente = data.cliente;

        // Recarregar os detalhes do cliente
        loadClienteDetails();
      } else {
        alert(`Erro ao atualizar cliente: ${data.message}`);
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar cliente:', error);
      alert(`Erro ao atualizar cliente: ${error.message}`);
    });
}

/**
 * Salva as alterações feitas no produto
 * @param {number} produtoId - ID do produto
 */
function saveProdutoChanges(produtoId) {
  // Obter os valores do formulário
  const formData = {};
  const form = document.getElementById('produto-edit-form');
  if (form) {
    const inputs = form.querySelectorAll('input, select');
    inputs.forEach((input) => {
      formData[input.name] = input.value;
    });
  }

  // Adicionar ID do produto
  formData.id = produtoId;

  // Primeiro, atualizar o produto
  const updatePromises = [
    fetch(`/api/produtos/${produtoId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('token')}`,
      },
      body: JSON.stringify(formData),
    }),
  ];

  // Se o NCM foi alterado, atualizar também no cenário
  const cenario = window.cenarioModal.currentCenario;
  if (formData.ncm && cenario.ncm !== formData.ncm) {
    updatePromises.push(
      fetch(
        `/api/cenarios/${window.cenarioModal.currentTipoTributo}/${cenario.id}`,
        {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            id: cenario.id,
            ncm: formData.ncm,
          }),
        },
      ),
    );
  }

  // Aguardar todas as atualizações
  Promise.all(updatePromises)
    .then((responses) => Promise.all(responses.map((res) => res.json())))
    .then((results) => {
      const [produtoResult, ...rest] = results;

      if (produtoResult.success) {
        // Atualizar os dados do produto no cenário atual
        window.cenarioModal.currentCenario.produto = produtoResult.produto;

        // Se o NCM foi atualizado, atualizar no cenário local também
        if (formData.ncm) {
          window.cenarioModal.currentCenario.ncm = formData.ncm;
        }

        alert('Dados atualizados com sucesso!');

        // Recarregar os detalhes do produto
        loadProdutoDetails();
      } else {
        throw new Error(produtoResult.message || 'Erro ao atualizar produto');
      }
    })
    .catch((error) => {
      console.error('Erro ao atualizar:', error);
      alert(`Erro ao atualizar: ${error.message}`);
    });
}
