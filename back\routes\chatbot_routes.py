"""
Rotas para o Chatbot IA do Sistema de Auditoria Fiscal
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import Usuario
from services.chatbot_ia_service import ChatbotIAService
import logging

# Configurar logger sem sobrescrever configurações globais
logger = logging.getLogger(__name__)

# Criar blueprint
chatbot_bp = Blueprint('chatbot', __name__)

# Instanciar serviço de IA
chatbot_service = ChatbotIAService()

@chatbot_bp.route('/chatbot/pergunta', methods=['POST'])
@jwt_required()
def processar_pergunta():
    """
    Processa uma pergunta do usuário e retorna resposta da IA
    """
    try:
        # Obter dados da requisição
        data = request.get_json()
        pergunta = data.get('pergunta', '').strip()
        empresa_id = data.get('empresa_id')
        registro_empresa = data.get('registro_empresa', False)
        proximo_campo = data.get('proximo_campo')
        dados_empresa = data.get('dados_empresa', {})

        # Obter usuário atual
        current_user_id = get_jwt_identity()
        usuario = Usuario.query.get(current_user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado',
                'resposta': 'Usuário não autenticado. Por favor, faça login novamente.'
            }), 401

        # Se estiver no meio de um registro de empresa
        if registro_empresa or proximo_campo:
            # Verificar permissões
            if usuario.tipo_usuario not in ['admin', 'escritorio']:
                return jsonify({
                    'success': False,
                    'resposta': 'Você não tem permissão para cadastrar empresas. Apenas administradores e usuários do tipo escritório podem realizar esta ação.',
                    'erro': True
                })
            
            # Atualizar dados da empresa no serviço
            if dados_empresa:
                chatbot_service.dados_empresa = {**chatbot_service.dados_empresa, **dados_empresa}
            
            # Adicionar o ID do usuário aos dados da empresa
            chatbot_service.dados_empresa['usuario_id'] = current_user_id
            
            # Processar o campo atual
            try:
                resultado = chatbot_service._processar_campo_empresa(
                    valor=pergunta,
                    campo=proximo_campo,
                    usuario_id=current_user_id
                )
                
                # Garantir que o resultado tenha a estrutura esperada
                if 'dados' not in resultado:
                    resultado['dados'] = {}
                
                # Atualizar dados da empresa no resultado
                resultado['dados'].update({
                    'dados_empresa': {**chatbot_service.dados_empresa},
                    'proximo_campo': resultado.get('proximo_campo')
                })
                
                # Se houver erro, retornar imediatamente
                if resultado.get('erro'):
                    return jsonify({
                        'success': False,
                        **resultado
                    })
                
                return jsonify({
                    'success': True,
                    **resultado
                })
                
            except Exception as e:
                logger.error(f"Erro ao processar campo da empresa: {str(e)}", exc_info=True)
                return jsonify({
                    'success': False,
                    'resposta': f'Ocorreu um erro ao processar os dados. Por favor, tente novamente.',
                    'erro': True,
                    'dados': {
                        'erro': True,
                        'mensagem': str(e)
                    }
                })
                
        # Se for uma solicitação para iniciar o registro de empresa
        if pergunta.lower() in ['cadastrar empresa', 'nova empresa', 'registrar empresa']:
            # Verificar permissões
            if usuario.tipo_usuario not in ['admin', 'escritorio']:
                return jsonify({
                    'success': False,
                    'resposta': 'Você não tem permissão para cadastrar empresas. Apenas administradores e usuários do tipo escritório podem realizar esta ação.',
                    'erro': True
                })
            
            # Iniciar o registro
            resultado = chatbot_service._iniciar_registro_empresa(current_user_id)
            chatbot_service.registro_empresa_em_andamento = True
            
            return jsonify({
                'success': True,
                **resultado,
                'registro_empresa': True
            })

        # Processar pergunta normal
        if not pergunta:
            return jsonify({
                'success': False,
                'resposta': 'Pergunta não pode estar vazia'
            })


        # Processar pergunta com IA
        resultado = chatbot_service.processar_pergunta(
            pergunta=pergunta,
            usuario_id=current_user_id,
            empresa_id=empresa_id
        )

        return jsonify(resultado)

    except Exception as e:
        logger.error(f"Erro ao processar pergunta: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'resposta': f'Desculpe, ocorreu um erro ao processar sua solicitação: {str(e)}',
            'erro': True
        }), 500

@chatbot_bp.route('/chatbot/historico', methods=['GET'])
@jwt_required()
def obter_historico():
    """
    Obtém o histórico de conversas do usuário
    """
    try:
        current_user_id = get_jwt_identity()
        limite = request.args.get('limite', 20, type=int)

        historico = chatbot_service.obter_historico(
            usuario_id=current_user_id,
            limite=limite
        )

        return jsonify({
            'success': True,
            'historico': historico
        })

    except Exception as e:
        logger.error(f"Erro ao obter histórico: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter histórico'
        }), 500

@chatbot_bp.route('/chatbot/avaliar', methods=['POST'])
@jwt_required()
def avaliar_resposta():
    """
    Permite ao usuário avaliar uma resposta do chatbot
    """
    try:
        data = request.get_json()
        conversa_id = data.get('conversa_id')
        avaliacao = data.get('avaliacao')  # 1-5
        feedback = data.get('feedback', '')

        if not conversa_id or not avaliacao:
            return jsonify({
                'success': False,
                'message': 'ID da conversa e avaliação são obrigatórios'
            }), 400

        if avaliacao < 1 or avaliacao > 5:
            return jsonify({
                'success': False,
                'message': 'Avaliação deve ser entre 1 e 5'
            }), 400

        sucesso = chatbot_service.avaliar_resposta(
            conversa_id=conversa_id,
            avaliacao=avaliacao,
            feedback=feedback
        )

        if sucesso:
            return jsonify({
                'success': True,
                'message': 'Avaliação salva com sucesso'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Erro ao salvar avaliação'
            }), 400

    except Exception as e:
        logger.error(f"Erro ao avaliar resposta: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao salvar avaliação'
        }), 500

@chatbot_bp.route('/chatbot/sugestoes', methods=['GET'])
@jwt_required()
def obter_sugestoes():
    """
    Retorna sugestões de perguntas para o usuário baseadas no contexto
    """
    try:
        # Obter parâmetros
        empresa_id = request.args.get('empresa_id', type=int)
        current_user_id = get_jwt_identity()
        usuario = Usuario.query.get(current_user_id)

        if not usuario:
            return jsonify({
                'success': False,
                'message': 'Usuário não encontrado'
            }), 404

        # Sugestões baseadas no contexto da empresa
        sugestoes_contextuais = []

        if empresa_id:
            # Buscar dados da empresa para sugestões contextuais
            from models import Empresa, NotaFiscalItem, AuditoriaResultado
            from sqlalchemy import func, desc

            empresa = Empresa.query.get(empresa_id)
            if empresa:
                # Verificar se há notas fiscais
                total_notas = NotaFiscalItem.query.filter_by(empresa_id=empresa_id).count()
                if total_notas > 0:
                    sugestoes_contextuais.extend([
                    ])

                # Verificar se há auditorias realizadas
                total_auditorias = AuditoriaResultado.query.filter_by(empresa_id=empresa_id).count()
                if total_auditorias > 0:
                    sugestoes_contextuais.extend([
                    ])

                # Buscar nota mais recente para sugestão específica
                nota_recente = NotaFiscalItem.query.filter_by(empresa_id=empresa_id)\
                    .order_by(desc(NotaFiscalItem.data_emissao)).first()
                if nota_recente:
                    sugestoes_contextuais.append(f"Me fale sobre a nota {nota_recente.numero_nf}")

        # Sugestões gerais sempre disponíveis
        sugestoes_gerais = [
        ]

        # Sugestões específicas por tipo de consulta
        sugestoes_especificas = [
            # Consultas sobre notas específicas
        ]

        # Combinar sugestões (priorizar contextuais)
        sugestoes = sugestoes_contextuais[:3] + sugestoes_gerais[:4] + sugestoes_especificas[:3]

        # Embaralhar para variedade
        import random
        random.shuffle(sugestoes)

        # Limitar a 8 sugestões
        sugestoes = sugestoes[:8]

        return jsonify({
            'success': True,
            'sugestoes': sugestoes
        })

    except Exception as e:
        logger.error(f"Erro ao obter sugestões: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Erro ao obter sugestões'
        }), 500

@chatbot_bp.route('/chatbot/status', methods=['GET'])
@jwt_required()
def verificar_status():
    """
    Verifica se o serviço de IA está funcionando
    """
    try:
        # Teste simples para verificar se a IA está respondendo
        resultado = chatbot_service.processar_pergunta(
            pergunta="teste",
            usuario_id=get_jwt_identity(),
            empresa_id=None
        )

        return jsonify({
            'success': True,
            'status': 'online',
            'message': 'Chatbot IA funcionando normalmente'
        })

    except Exception as e:
        logger.error(f"Erro ao verificar status: {str(e)}")
        return jsonify({
            'success': False,
            'status': 'offline',
            'message': 'Chatbot IA indisponível',
            'erro': str(e)
        }), 500
