from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, CenarioICMS, CenarioICMSST, CenarioIPI, CenarioPIS, CenarioCOFINS, CenarioDIFAL, Usuario
from services.cenario_service import CenarioService
from services.tributo_calculation_service import TributoCalculationService
from datetime import datetime
from sqlalchemy import extract, distinct
from collections import defaultdict
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Criar blueprint
cenario_bp = Blueprint('cenario', __name__)

# Mapeamento de tipos de tributo para modelos
TIPO_TRIBUTO_MODELS = {
    'icms': CenarioICMS,
    'icms_st': CenarioICMSST,
    'ipi': CenarioIPI,
    'pis': CenarioPIS,
    'cofins': CenarioCOFINS,
    'difal': CenarioDIFAL
}

@cenario_bp.route('/api/cenarios/<tipo_tributo>', methods=['GET'])
def listar_cenarios(tipo_tributo):
    """
    Lista cenários de um tipo específico com suporte a paginação

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros da query
        empresa_id = request.args.get('empresa_id', type=int)
        cliente_id = request.args.get('cliente_id', type=int)
        produto_id = request.args.get('produto_id', type=int)
        status = request.args.get('status')
        ativo = request.args.get('ativo', type=bool)
        direcao = request.args.get('direcao')

        # Parâmetros de paginação
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 100, type=int)  # Default 100 itens por página
        load_all = request.args.get('load_all', 'false').lower() == 'true'

        # Se load_all for true, carregar todos os registros sem paginação
        if load_all:
            per_page = None  # Sem limite
        else:
            # Limitar o número máximo de itens por página para evitar sobrecarga (apenas quando não for load_all)
            if per_page > 500:
                per_page = 500

        # Construir query
        query = TIPO_TRIBUTO_MODELS[tipo_tributo].query

        # Aplicar filtros
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)
        if cliente_id:
            query = query.filter_by(cliente_id=cliente_id)
        if produto_id:
            query = query.filter_by(produto_id=produto_id)
        if direcao:
            query = query.filter_by(direcao=direcao)
            # Adicionar filtro de tipo_operacao
            tipo_operacao = '0' if direcao == 'entrada' else '1'
            query = query.filter_by(tipo_operacao=tipo_operacao)
        if status:
            # Tratamento especial para status 'inconsistente' para incluir também os status com prefixo 'incons_'
            if status == 'inconsistente':
                query = query.filter(
                    db.or_(
                        TIPO_TRIBUTO_MODELS[tipo_tributo].status == 'inconsistente',
                        TIPO_TRIBUTO_MODELS[tipo_tributo].status.like('incons_%')
                    )
                )
            else:
                query = query.filter_by(status=status)
        if ativo is not None:
            query = query.filter_by(ativo=ativo)

        # Contar total de registros para paginação
        total_count = query.count()

        # Aplicar paginação ou carregar todos os registros
        if load_all:
            # Carregar todos os registros sem paginação
            cenarios_items = query.order_by(TIPO_TRIBUTO_MODELS[tipo_tributo].id.desc()).all()
            # Criar objeto simulando paginação para compatibilidade
            class MockPagination:
                def __init__(self, items, total):
                    self.items = items
                    self.total = total
                    self.pages = 1
                    self.page = 1
                    self.per_page = len(items)
                    self.has_prev = False
                    self.has_next = False
                    self.prev_num = None
                    self.next_num = None

            cenarios = MockPagination(cenarios_items, total_count)
        else:
            # Aplicar paginação normal
            cenarios = query.order_by(TIPO_TRIBUTO_MODELS[tipo_tributo].id.desc()).paginate(page=page, per_page=per_page, error_out=False)

        # Converter para dicionários e adicionar informações de cliente e produto
        cenarios_dict = []
        for cenario in cenarios.items:
            cenario_dict = cenario.to_dict()

            # Adicionar informações do cliente
            if cenario.cliente:
                cenario_dict['cliente'] = cenario.cliente.to_dict()

            # Adicionar informações do produto
            if cenario.produto:
                produto_dict = cenario.produto.to_dict()
                # Log para debug - verificar se CEST está sendo carregado
                logger.debug(f"Produto {cenario.produto.id} - CEST: {cenario.produto.cest}")
                cenario_dict['produto'] = produto_dict

            cenarios_dict.append(cenario_dict)

        # Retornar dados com informações de paginação
        if load_all:
            # Para load_all, retornar informações simplificadas
            return jsonify({
                'success': True,
                'cenarios': cenarios_dict,
                'pagination': {
                    'total': total_count,
                    'per_page': len(cenarios_dict),
                    'current_page': 1,
                    'last_page': 1,
                    'from': 1 if cenarios_dict else 0,
                    'to': len(cenarios_dict) if cenarios_dict else 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_page': None,
                    'next_page': None
                }
            })
        else:
            # Paginação normal
            return jsonify({
                'success': True,
                'cenarios': cenarios_dict,
                'pagination': {
                    'total': total_count,
                    'per_page': per_page,
                    'current_page': page,
                    'last_page': cenarios.pages,
                    'from': (page - 1) * per_page + 1 if cenarios.items else 0,
                    'to': (page - 1) * per_page + len(cenarios.items) if cenarios.items else 0,
                    'has_prev': cenarios.has_prev,
                    'has_next': cenarios.has_next,
                    'prev_page': cenarios.prev_num if cenarios.has_prev else None,
                    'next_page': cenarios.next_num if cenarios.has_next else None
                }
            })
    except Exception as e:
        logger.error(f"Erro ao listar cenários: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao listar cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/filter-options', methods=['GET'])
@jwt_required()
def obter_opcoes_filtro_cenarios(tipo_tributo):
    """
    Obtém as opções para os filtros de cenários com relacionamentos

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)
        direcao = request.args.get('direcao', 'saida')
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)
        status = request.args.get('status', 'novo')

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        # Obter o modelo correspondente
        CenarioModel = TIPO_TRIBUTO_MODELS[tipo_tributo]

        # Construir a query base
        query = CenarioModel.query.filter_by(empresa_id=empresa_id)

        # Adicionar filtro de direção
        query = query.filter_by(direcao=direcao)

        # Adicionar filtro de tipo_operacao
        tipo_operacao = '0' if direcao == 'entrada' else '1'
        query = query.filter_by(tipo_operacao=tipo_operacao)

        # Adicionar filtro de status se especificado
        if status:
            query = query.filter_by(status=status)

        # Para filtros de ano e mês, precisamos fazer join com a tabela tributo
        # pois os cenários não têm data_emissao diretamente
        if year or month:
            from models import Tributo
            query = query.join(Tributo,
                (Tributo.empresa_id == CenarioModel.empresa_id) &
                (Tributo.cliente_id == CenarioModel.cliente_id) &
                (Tributo.produto_id == CenarioModel.produto_id)
            )

            if year:
                query = query.filter(extract('year', Tributo.data_emissao) == year)

            if month:
                query = query.filter(extract('month', Tributo.data_emissao) == month)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todos os cenários
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem cenários do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas cenários das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(CenarioModel.empresa_id.in_(empresas_permitidas))

        # Carregar os relacionamentos necessários
        query = query.options(
            db.joinedload(CenarioModel.cliente)
        )

        # Obter valores únicos para cada campo de filtro
        cfops = defaultdict(lambda: {'ncms': set(), 'csts': set(), 'aliquotas': set(), 'estados': set(), 'reducoes': set()})
        ncms = defaultdict(lambda: {'cfops': set(), 'csts': set(), 'aliquotas': set(), 'estados': set(), 'reducoes': set()})
        csts = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'aliquotas': set(), 'estados': set(), 'reducoes': set()})
        aliquotas = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'estados': set(), 'reducoes': set()})
        estados = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'reducoes': set()})
        reducoes = defaultdict(lambda: {'cfops': set(), 'ncms': set(), 'csts': set(), 'aliquotas': set(), 'estados': set()})

        # Executar query e processar resultados
        cenarios = query.all()

        def format_percentage_value(val):
            """Formatar valor percentual para remover zeros desnecessários"""
            if val is None or val == '':
                return '0'
            try:
                # Converter para número, remover zeros à direita e ponto final se necessário
                num = float(val)
                if num == 0:
                    return '0'
                # Usar toFixed para garantir no máximo 4 casas decimais, depois remover zeros à direita
                str_val = f"{num:.4f}"
                str_val = str_val.rstrip('0').rstrip('.')
                return str_val
            except (ValueError, TypeError):
                return '0'

        for cenario in cenarios:
            cfop = cenario.cfop or ''
            ncm = cenario.ncm or ''
            cst = cenario.cst or ''
            aliquota = format_percentage_value(cenario.aliquota or '0')

            # Obter estado do cliente
            estado = cenario.cliente.uf if cenario.cliente and cenario.cliente.uf else ''

            # Obter redução com base no tipo de tributo
            reducao = '0'
            if tipo_tributo in ['icms', 'icms_st', 'pis', 'cofins', 'difal']:
                reducao = format_percentage_value(getattr(cenario, 'p_red_bc', '0') or '0')
            elif tipo_tributo == 'ipi':
                # IPI não tem percentual de redução
                reducao = '0'

            if cfop:
                cfops[cfop]['ncms'].add(ncm)
                cfops[cfop]['csts'].add(cst)
                cfops[cfop]['aliquotas'].add(aliquota)
                cfops[cfop]['estados'].add(estado)
                cfops[cfop]['reducoes'].add(reducao)

            if ncm:
                ncms[ncm]['cfops'].add(cfop)
                ncms[ncm]['csts'].add(cst)
                ncms[ncm]['aliquotas'].add(aliquota)
                ncms[ncm]['estados'].add(estado)
                ncms[ncm]['reducoes'].add(reducao)

            if cst:
                csts[cst]['cfops'].add(cfop)
                csts[cst]['ncms'].add(ncm)
                csts[cst]['aliquotas'].add(aliquota)
                csts[cst]['estados'].add(estado)
                csts[cst]['reducoes'].add(reducao)

            if aliquota:
                aliquotas[aliquota]['cfops'].add(cfop)
                aliquotas[aliquota]['ncms'].add(ncm)
                aliquotas[aliquota]['csts'].add(cst)
                aliquotas[aliquota]['estados'].add(estado)
                aliquotas[aliquota]['reducoes'].add(reducao)

        # Inicializar dicionários para os novos filtros
        estados = {}
        reducoes = {}

        # Processar estados e reduções dos cenários
        for cenario in cenarios:
            # Obter estado do cliente
            estado = cenario.cliente.uf if cenario.cliente and cenario.cliente.uf else ''

            # Obter redução com base no tipo de tributo
            reducao = '0'
            if tipo_tributo in ['icms', 'icms_st', 'pis', 'cofins', 'difal']:
                reducao = format_percentage_value(getattr(cenario, 'p_red_bc', '0') or '0')
            elif tipo_tributo == 'ipi':
                # IPI não tem percentual de redução
                reducao = '0'

            # Processar estado
            if estado:
                if estado not in estados:
                    estados[estado] = {
                        'cfops': set(),
                        'ncms': set(),
                        'csts': set(),
                        'aliquotas': set(),
                        'reducoes': set()
                    }

                if cenario.cfop:
                    estados[estado]['cfops'].add(cenario.cfop)
                if cenario.ncm:
                    estados[estado]['ncms'].add(cenario.ncm)
                if cenario.cst:
                    estados[estado]['csts'].add(cenario.cst)
                if cenario.aliquota is not None:
                    estados[estado]['aliquotas'].add(format_percentage_value(cenario.aliquota))
                if reducao and reducao != '0':
                    estados[estado]['reducoes'].add(reducao)

            # Processar redução
            if reducao and reducao != '0':
                if reducao not in reducoes:
                    reducoes[reducao] = {
                        'cfops': set(),
                        'ncms': set(),
                        'csts': set(),
                        'aliquotas': set(),
                        'estados': set()
                    }

                if cenario.cfop:
                    reducoes[reducao]['cfops'].add(cenario.cfop)
                if cenario.ncm:
                    reducoes[reducao]['ncms'].add(cenario.ncm)
                if cenario.cst:
                    reducoes[reducao]['csts'].add(cenario.cst)
                if cenario.aliquota is not None:
                    reducoes[reducao]['aliquotas'].add(format_percentage_value(cenario.aliquota))
                if estado:
                    reducoes[reducao]['estados'].add(estado)

        # Converter sets para listas e formatar resposta
        opcoes = {
            'cfops': [{'value': cfop, 'related': {
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0)
            }} for cfop, data in sorted(cfops.items()) if cfop],

            'ncms': [{'value': ncm, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0)
            }} for ncm, data in sorted(ncms.items()) if ncm],

            'csts': [{'value': cst, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0)
            }} for cst, data in sorted(csts.items()) if cst],

            'aliquotas': [{'value': aliquota, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'estados': sorted(list(data['estados'])),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0)
            }} for aliquota, data in sorted(aliquotas.items(), key=lambda x: float(x[0]) if x[0] else 0) if aliquota],

            'estados': [{'value': estado, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'reducoes': sorted(list(data['reducoes']), key=lambda x: float(x) if x else 0)
            }} for estado, data in sorted(estados.items()) if estado],

            'reducoes': [{'value': reducao, 'related': {
                'cfops': sorted(list(data['cfops'])),
                'ncms': sorted(list(data['ncms'])),
                'csts': sorted(list(data['csts'])),
                'aliquotas': sorted(list(data['aliquotas']), key=lambda x: float(x) if x else 0),
                'estados': sorted(list(data['estados']))
            }} for reducao, data in sorted(reducoes.items(), key=lambda x: float(x[0]) if x[0] else 0) if reducao and reducao != '0']
        }

        return jsonify({
            "success": True,
            "opcoes": opcoes
        }), 200

    except Exception as e:
        logger.error(f"Erro ao obter opções de filtro de cenários: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter opções de filtro de cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['GET'])
def obter_cenario(tipo_tributo, cenario_id):
    """
    Obtém um cenário específico

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Buscar cenário
        cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        # Converter para dicionário
        cenario_dict = cenario.to_dict()

        # Adicionar informações do cliente
        if cenario.cliente:
            cenario_dict['cliente'] = cenario.cliente.to_dict()

        # Adicionar informações do produto
        if cenario.produto:
            cenario_dict['produto'] = cenario.produto.to_dict()

        return jsonify({
            'success': True,
            'cenario': cenario_dict
        })
    except Exception as e:
        logger.error(f"Erro ao obter cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao obter cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/status', methods=['PUT'])
def atualizar_status_cenario(tipo_tributo, cenario_id):
    """
    Atualiza o status de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        novo_status = data.get('status')
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not novo_status:
            return jsonify({
                'success': False,
                'message': 'Status não informado'
            }), 400

        if novo_status not in ['novo', 'producao', 'inconsistente']:
            return jsonify({
                'success': False,
                'message': f'Status inválido: {novo_status}'
            }), 400

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Verificar se o cenário existe
        CenarioModel = cenario_service._get_cenario_model(tipo_tributo)
        cenario_atual = CenarioModel.query.get(cenario_id)

        if not cenario_atual:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        # Se estivermos promovendo para produção, verificar se há sobreposição de datas
        data_inicio_vigencia = None
        if novo_status == 'producao' and cenario_atual.status != 'producao':
            # Se o cenário atual já tem uma data de início de vigência, usá-la
            if cenario_atual.data_inicio_vigencia:
                data_inicio_vigencia = cenario_atual.data_inicio_vigencia
                logger.info(f"Usando data de início de vigência do cenário atual: {data_inicio_vigencia}")
            else:
                # Se não tiver data de início de vigência, buscar a data de emissão da nota fiscal mais recente
                from models import NotaFiscalItem, Tributo

                # Buscar a nota fiscal mais recente para este produto e cliente
                nota_fiscal = NotaFiscalItem.query.filter_by(
                    empresa_id=cenario_atual.empresa_id,
                    cliente_id=cenario_atual.cliente_id,
                    produto_id=cenario_atual.produto_id
                ).order_by(NotaFiscalItem.data_emissao.desc()).first()

                if nota_fiscal:
                    data_inicio_vigencia = nota_fiscal.data_emissao
                    logger.info(f"Data de emissão encontrada na nota fiscal: {data_inicio_vigencia}")
                else:
                    # Se não encontrar a nota fiscal, buscar no tributo
                    tributo = Tributo.query.filter_by(
                        empresa_id=cenario_atual.empresa_id,
                        cliente_id=cenario_atual.cliente_id,
                        produto_id=cenario_atual.produto_id
                    ).order_by(Tributo.data_emissao.desc()).first()

                    if tributo:
                        data_inicio_vigencia = tributo.data_emissao
                        logger.info(f"Data de emissão encontrada no tributo: {data_inicio_vigencia}")
                    else:
                        # Se não encontrar de jeito nenhum, usar a data atual
                        from datetime import datetime
                        data_inicio_vigencia = datetime.now().date()
                        logger.warning(f"Nenhuma data de emissão encontrada. Usando data atual: {data_inicio_vigencia}")

                        # Definir a data de início de vigência no cenário atual para referência futura
                        cenario_atual.data_inicio_vigencia = data_inicio_vigencia
                        db.session.commit()
                        logger.info(f"Atualizada a data de início de vigência do cenário {cenario_id} para {data_inicio_vigencia}")

            # Garantir que a data de início de vigência foi definida
            if not data_inicio_vigencia:
                raise ValueError("Não foi possível determinar a data de início de vigência para o cenário.")

            logger.info(f"Data de início de vigência definida como: {data_inicio_vigencia}")

        try:
            # Atualizar status do cenário
            cenario = cenario_service.atualizar_status_cenario(cenario_id, tipo_tributo, novo_status, data_inicio_vigencia)

            if not cenario:
                return jsonify({
                    'success': False,
                    'message': f'Cenário não encontrado: {cenario_id}'
                }), 404

        except ValueError as e:
            # Capturar erros de validação e retornar mensagem amigável
            logger.error(f"Erro de validação ao atualizar status do cenário: {str(e)}")
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        except Exception as e:
            # Capturar outros erros inesperados
            logger.error(f"Erro inesperado ao atualizar status do cenário: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': 'Ocorreu um erro inesperado ao atualizar o status do cenário. Por favor, tente novamente.'
            }), 500

        # Se o status foi atualizado para 'producao', recalcular tributos
        if novo_status == 'producao':
            # Inicializar serviço de cálculo de tributos
            tributo_calculation_service = TributoCalculationService(empresa_id)

            # Recalcular tributos para o produto
            resultado = tributo_calculation_service.calculate_tributos(produto_ids=[cenario.produto_id])

            # Verificar cenários compatíveis
            cenarios_compativeis = cenario_service.verificar_cenarios_compativeis(cenario.cliente_id, cenario.produto_id, tipo_tributo)

            return jsonify({
                'success': True,
                'cenario': cenario.to_dict(),
                'calculo_tributos': resultado,
                'cenarios_compativeis': cenarios_compativeis
            })

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        logger.error(f"Erro ao processar requisição de atualização de status: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'message': 'Ocorreu um erro ao processar sua solicitação. Por favor, tente novamente.'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/ativar', methods=['PUT'])
def ativar_cenario(tipo_tributo, cenario_id):
    """
    Ativa um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Ativar o cenário
        try:
            cenario = cenario_service.ativar_cenario(cenario_id, tipo_tributo)
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        logger.error(f"Erro ao ativar cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao ativar cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/desativar', methods=['PUT'])
def desativar_cenario(tipo_tributo, cenario_id):
    """
    Desativa um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Desativar o cenário
        cenario = cenario_service.desativar_cenario(cenario_id, tipo_tributo)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        logger.error(f"Erro ao desativar cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao desativar cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>/vigencia', methods=['PUT'])
def atualizar_vigencia_cenario(tipo_tributo, cenario_id):
    """
    Atualiza a vigência de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        data_inicio_vigencia_str = data.get('data_inicio_vigencia')
        data_fim_vigencia_str = data.get('data_fim_vigencia')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        if not data_inicio_vigencia_str:
            return jsonify({
                'success': False,
                'message': 'Data de início de vigência não informada'
            }), 400

        # Converter datas
        try:
            data_inicio_vigencia = datetime.fromisoformat(data_inicio_vigencia_str)
        except ValueError:
            return jsonify({
                'success': False,
                'message': f'Data de início de vigência inválida: {data_inicio_vigencia_str}'
            }), 400

        data_fim_vigencia = None
        if data_fim_vigencia_str:
            try:
                data_fim_vigencia = datetime.fromisoformat(data_fim_vigencia_str)
            except ValueError:
                return jsonify({
                    'success': False,
                    'message': f'Data de fim de vigência inválida: {data_fim_vigencia_str}'
                }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Atualizar vigência do cenário
        try:
            cenario = cenario_service.atualizar_vigencia_cenario(
                cenario_id,
                tipo_tributo,
                data_inicio_vigencia,
                data_fim_vigencia
            )
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        return jsonify({
            'success': True,
            'cenario': cenario.to_dict()
        })
    except Exception as e:
        logger.error(f"Erro ao atualizar vigência do cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar vigência do cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/bulk-status', methods=['PUT'])
def atualizar_status_cenarios_em_massa(tipo_tributo):
    """
    Atualiza o status de múltiplos cenários de uma vez

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        cenario_ids = data.get('cenario_ids', [])
        novo_status = data.get('status', 'producao')  # Default para 'producao'
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        data_inicio_vigencia_str = data.get('data_inicio_vigencia')

        # Validar dados
        if not cenario_ids or not isinstance(cenario_ids, list):
            return jsonify({
                'success': False,
                'message': 'Lista de IDs de cenários não informada ou inválida'
            }), 400

        if novo_status not in ['novo', 'producao', 'inconsistente']:
            return jsonify({
                'success': False,
                'message': f'Status inválido: {novo_status}'
            }), 400

        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Obter data de início de vigência para status 'producao'
        data_inicio_vigencia = None
        if novo_status == 'producao':
            # Se a data de início de vigência foi fornecida, usá-la
            if data_inicio_vigencia_str:
                try:
                    data_inicio_vigencia = datetime.fromisoformat(data_inicio_vigencia_str)
                except ValueError:
                    return jsonify({
                        'success': False,
                        'message': f'Data de início de vigência inválida: {data_inicio_vigencia_str}'
                    }), 400
            # Se não foi fornecida, a data será buscada no serviço para cada cenário

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Atualizar status dos cenários
        updated_count = 0
        errors = []

        for cenario_id in cenario_ids:
            try:
                cenario = cenario_service.atualizar_status_cenario(
                    cenario_id,
                    tipo_tributo,
                    novo_status,
                    data_inicio_vigencia
                )
                if cenario:
                    updated_count += 1
            except Exception as e:
                errors.append(f"Erro ao atualizar cenário {cenario_id}: {str(e)}")
                logger.error(f"Erro ao atualizar cenário {cenario_id}: {str(e)}")

        # Se o status foi atualizado para 'producao', recalcular tributos
        if novo_status == 'producao' and updated_count > 0:
            # Inicializar serviço de cálculo de tributos
            tributo_calculation_service = TributoCalculationService(empresa_id)

            # Obter produtos únicos dos cenários atualizados
            produto_ids = []
            for cenario_id in cenario_ids:
                cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)
                if cenario and cenario.produto_id not in produto_ids:
                    produto_ids.append(cenario.produto_id)

            # Recalcular tributos para os produtos
            if produto_ids:
                tributo_calculation_service.calculate_tributos(produto_ids=produto_ids)

        return jsonify({
            'success': True,
            'message': f'{updated_count} cenários atualizados com sucesso',
            'updated_count': updated_count,
            'errors': errors
        })
    except Exception as e:
        logger.error(f"Erro ao atualizar status dos cenários em massa: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar status dos cenários em massa: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/count', methods=['GET'])
def contar_cenarios(tipo_tributo):
    """
    Conta os cenários por status

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter parâmetros da requisição
        empresa_id = request.args.get('empresa_id')
        direcao = request.args.get('direcao')
        year = request.args.get('year')
        month = request.args.get('month')

        # Validar parâmetros
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not direcao:
            return jsonify({
                'success': False,
                'message': 'Direção não informada'
            }), 400

        # Obter o modelo correspondente
        CenarioModel = TIPO_TRIBUTO_MODELS[tipo_tributo]

        # Construir a query base
        query = CenarioModel.query.filter_by(empresa_id=empresa_id)

        # Adicionar filtro de direção
        query = query.filter_by(direcao=direcao)

        # Adicionar filtro de tipo_operacao
        tipo_operacao = '0' if direcao == 'entrada' else '1'
        query = query.filter_by(tipo_operacao=tipo_operacao)

        # Adicionar filtros de ano e mês, se informados
        if year:
            query = query.filter(extract('year', CenarioModel.data_emissao) == year)

        if month:
            query = query.filter(extract('month', CenarioModel.data_emissao) == month)

        # Contar cenários por status
        counts = {}
        for status in ['novo', 'producao', 'inconsistente']:
            if status == 'inconsistente':
                # Contar tanto 'inconsistente' quanto os que começam com 'incons_'
                count = query.filter(
                    db.or_(
                        CenarioModel.status == 'inconsistente',
                        CenarioModel.status.like('incons_%')
                    )
                ).count()
            else:
                count = query.filter_by(status=status).count()
            counts[status] = count

        return jsonify({
            'success': True,
            'counts': counts
        })
    except Exception as e:
        logger.error(f"Erro ao contar cenários: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao contar cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['DELETE'])
def excluir_cenario(tipo_tributo, cenario_id):
    """
    Exclui um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.args
        empresa_id = data.get('empresa_id', type=int)
        escritorio_id = data.get('escritorio_id', type=int)

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Excluir o cenário
        resultado = cenario_service.excluir_cenario(cenario_id, tipo_tributo)

        if not resultado:
            return jsonify({
                'success': False,
                'message': 'Não foi possível excluir o cenário. Verifique se ele existe e não está em produção ativo.'
            }), 400

        return jsonify({
            'success': True,
            'message': 'Cenário excluído com sucesso'
        })
    except Exception as e:
        logger.error(f"Erro ao excluir cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao excluir cenário: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/reverificar-status', methods=['POST'])
def reverificar_status_cenarios(tipo_tributo):
    """
    Reverifica o status de cenários

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')
        produto_id = data.get('produto_id')  # Opcional

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Inicializar serviço de cenários
        cenario_service = CenarioService(empresa_id, escritorio_id)

        # Reverificar status dos cenários
        try:
            # Se produto_id for informado, reverificar apenas para este produto
            if produto_id:
                cenarios_atualizados = cenario_service.reverificar_status_cenarios_produto(
                    tipo_tributo,
                    produto_id
                )
                mensagem = f'Status de {cenarios_atualizados} cenários reverificados para o produto {produto_id}'
            else:
                cenarios_atualizados = cenario_service.reverificar_status_cenarios(tipo_tributo)
                mensagem = f'Status de {cenarios_atualizados} cenários reverificados'
        except ValueError as e:
            return jsonify({
                'success': False,
                'message': str(e)
            }), 400

        return jsonify({
            'success': True,
            'message': mensagem,
            'cenarios_atualizados': cenarios_atualizados
        })
    except Exception as e:
        logger.error(f"Erro ao reverificar status dos cenários: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao reverificar status dos cenários: {str(e)}'
        }), 500

@cenario_bp.route('/api/cenarios/<tipo_tributo>/<int:cenario_id>', methods=['PUT'])
def atualizar_cenario(tipo_tributo, cenario_id):
    """
    Atualiza os dados de um cenário

    Args:
        tipo_tributo (str): Tipo de tributo ('icms', 'icms_st', 'ipi', 'pis', 'cofins', 'difal')
        cenario_id (int): ID do cenário
    """
    try:
        # Verificar se o tipo de tributo é válido
        if tipo_tributo not in TIPO_TRIBUTO_MODELS:
            return jsonify({
                'success': False,
                'message': f'Tipo de tributo inválido: {tipo_tributo}'
            }), 400

        # Obter dados da requisição
        data = request.json
        empresa_id = data.get('empresa_id')
        escritorio_id = data.get('escritorio_id')

        # Validar dados
        if not empresa_id:
            return jsonify({
                'success': False,
                'message': 'ID da empresa não informado'
            }), 400

        if not escritorio_id:
            return jsonify({
                'success': False,
                'message': 'ID do escritório não informado'
            }), 400

        # Buscar cenário
        cenario = TIPO_TRIBUTO_MODELS[tipo_tributo].query.get(cenario_id)

        if not cenario:
            return jsonify({
                'success': False,
                'message': f'Cenário não encontrado: {cenario_id}'
            }), 404

        # Atualizar campos do cenário com base no tipo de tributo
        if tipo_tributo == 'icms':
            # Atualizar campos específicos do ICMS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_dif' in data:
                cenario.p_dif = data.get('p_dif')
        elif tipo_tributo == 'icms_st':
            # Atualizar campos específicos do ICMS-ST
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'icms_st_mod_bc' in data:
                cenario.icms_st_mod_bc = data.get('icms_st_mod_bc')
            if 'icms_st_p_mva' in data:
                cenario.icms_st_p_mva = data.get('icms_st_p_mva')
            if 'icms_st_aliquota' in data:
                cenario.icms_st_aliquota = data.get('icms_st_aliquota')
            if 'icms_st_p_red_bc' in data:
                cenario.icms_st_p_red_bc = data.get('icms_st_p_red_bc')
        elif tipo_tributo == 'ipi':
            # Atualizar campos específicos do IPI
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'codigo_enquadramento' in data:
                cenario.codigo_enquadramento = data.get('codigo_enquadramento')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'ex' in data:
                cenario.ex = data.get('ex')
        elif tipo_tributo == 'pis':
            # Atualizar campos específicos do PIS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
        elif tipo_tributo == 'cofins':
            # Atualizar campos específicos do COFINS
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
        elif tipo_tributo == 'difal':
            # Atualizar campos específicos do DIFAL
            if 'cst' in data:
                cenario.cst = data.get('cst')
            if 'origem' in data:
                cenario.origem = data.get('origem')
            if 'mod_bc' in data:
                cenario.mod_bc = data.get('mod_bc')
            if 'p_red_bc' in data:
                cenario.p_red_bc = data.get('p_red_bc')
            if 'aliquota' in data:
                cenario.aliquota = data.get('aliquota')
            if 'p_fcp_uf_dest' in data:
                cenario.p_fcp_uf_dest = data.get('p_fcp_uf_dest')
            if 'p_icms_uf_dest' in data:
                cenario.p_icms_uf_dest = data.get('p_icms_uf_dest')
            if 'p_icms_inter' in data:
                cenario.p_icms_inter = data.get('p_icms_inter')
            if 'p_icms_inter_part' in data:
                cenario.p_icms_inter_part = data.get('p_icms_inter_part')

        # Atualizar data de atualização
        cenario.data_atualizacao = datetime.now()

        # Verificar se campos críticos foram alterados
        campos_criticos = False
        if tipo_tributo == 'icms' and any(field in data for field in ['cst', 'aliquota', 'p_red_bc', 'mod_bc']):
            campos_criticos = True
        elif tipo_tributo == 'icms_st' and any(field in data for field in ['cst', 'aliquota', 'p_red_bc', 'mod_bc', 'icms_st_aliquota', 'icms_st_p_mva']):
            campos_criticos = True
        elif tipo_tributo in ['ipi', 'pis', 'cofins'] and any(field in data for field in ['cst', 'aliquota']):
            campos_criticos = True
        elif tipo_tributo == 'difal' and any(field in data for field in ['cst', 'aliquota', 'p_fcp_uf_dest', 'p_icms_uf_dest']):
            campos_criticos = True

        # Salvar alterações
        db.session.commit()

        # Se campos críticos foram alterados, recalcular tributos afetados
        if campos_criticos and cenario.empresa_id and cenario.produto_id:
            try:
                from services.tributo_calculation_service import TributoCalculationService
                calculation_service = TributoCalculationService(cenario.empresa_id)

                # Buscar tributos que usam este produto e cliente
                from models import Tributo
                tributos = Tributo.query.filter_by(
                    empresa_id=cenario.empresa_id,
                    produto_id=cenario.produto_id,
                    cliente_id=cenario.cliente_id
                ).all()

                # Recalcular apenas o tipo de tributo específico que foi alterado
                for tributo in tributos:
                    calculation_service._calculate_tributo_values(tributo, tipo_tributo)

                # Salvar alterações
                db.session.commit()

                logger.info(f"Tributos {tipo_tributo} recalculados para {len(tributos)} registros após atualização de cenário")
            except Exception as e:
                logger.error(f"Erro ao recalcular tributos após atualização de cenário: {str(e)}")
                # Fazer rollback em caso de erro
                db.session.rollback()

        # Converter para dicionário
        cenario_dict = cenario.to_dict()

        # Adicionar informações do cliente
        if cenario.cliente:
            cenario_dict['cliente'] = cenario.cliente.to_dict()

        # Adicionar informações do produto
        if cenario.produto:
            cenario_dict['produto'] = cenario.produto.to_dict()

        # Preparar resposta
        response_data = {
            'success': True,
            'message': 'Cenário atualizado com sucesso',
            'campos_criticos_alterados': campos_criticos,
            'cenario': cenario_dict
        }

        # Adicionar informações sobre recálculo se foi realizado
        if campos_criticos and cenario.empresa_id and cenario.produto_id:
            response_data['recalculo_realizado'] = True
            response_data['tipo_tributo_recalculado'] = tipo_tributo

        return jsonify(response_data)
    except Exception as e:
        logger.error(f"Erro ao atualizar cenário: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Erro ao atualizar cenário: {str(e)}'
        }), 500