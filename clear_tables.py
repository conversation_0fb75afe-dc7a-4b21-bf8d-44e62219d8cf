import os
import psycopg2
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Database connection details
db_name = os.getenv("DB_NAME")
db_user = os.getenv("DB_USER")
db_password = os.getenv("DB_PASSWORD")
db_host = os.getenv("DB_HOST")
db_port = os.getenv("DB_PORT")

# Tables to clear
# Note: 'log_atividade' was requested but not found in the initial file listing.
# The script will attempt to truncate it; ensure it exists in your schema.
tables_to_clear = [
    # "cliente",
    # "importacao_xml",
    # "log_atividade",
    # "produto",
    # "tributo",
    # "cenario_icms",
    # "cenario_icms_st",
    # "cenario_ipi",
    # "cenario_pis",
    # "cenario_cofins",
    # "cenario_difal",
    # "nota_fiscal_item",
    # "auditoria_resultado",
    # "auditoria_sumario"
    "cliente_entrada",
    "produto_entrada",
    "nota_entrada",
    "item_nota_entrada",
    "importacao_sped"
]

conn = None
cur = None

try:
    # Establish database connection
    print(f"Connecting to database '{db_name}' on {db_host}:{db_port}...")
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    conn.autocommit = False # Start transaction
    cur = conn.cursor()
    print("Connection successful.")

    # Truncate tables
    for table in tables_to_clear:
        try:
            print(f"Attempting to clear table '{table}'...")
            # Using TRUNCATE ... RESTART IDENTITY CASCADE
            # RESTART IDENTITY resets sequences associated with the table's identity columns.
            # CASCADE removes rows in referencing tables via foreign keys. Use carefully.
            sql = f"TRUNCATE TABLE {table} RESTART IDENTITY CASCADE;"
            cur.execute(sql)
            print(f"Table '{table}' cleared successfully.")
        except psycopg2.Error as e:
            print(f"Error clearing table '{table}': {e}")
            # Decide if you want to rollback immediately or continue with other tables
            # For now, we'll print the error and continue, then rollback at the end if any error occurred.
            raise  # Re-raise the exception to trigger the outer rollback

    # Commit the transaction if all truncates were successful
    conn.commit()
    print("\nAll specified tables cleared successfully. Transaction committed.")

except psycopg2.OperationalError as e:
    print(f"\nDatabase connection error: {e}")
    if conn:
        conn.rollback() # Rollback any partial changes if connection failed mid-operation
except psycopg2.Error as e:
    print(f"\nAn error occurred during table clearing: {e}")
    if conn:
        print("Rolling back transaction.")
        conn.rollback() # Rollback transaction on any SQL error during truncate
except Exception as e:
    print(f"\nAn unexpected error occurred: {e}")
    if conn:
        conn.rollback()
finally:
    # Close cursor and connection
    if cur:
        cur.close()
    if conn:
        conn.close()
        print("Database connection closed.")