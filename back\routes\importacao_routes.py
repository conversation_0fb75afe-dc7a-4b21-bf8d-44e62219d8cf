from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity
from models import db, Usuario, ImportacaoXML, ImportacaoSped, Empresa
from services import XMLImportService
from services.batch_xml_import_service import BatchXMLImportService
from services.sped_import_service import SPEDImportService
from utils.xml_processor import XMLProcessor
from utils.sped_processor import SPEDProcessor
import os
from werkzeug.utils import secure_filename
from typing import List

importacao_bp = Blueprint('importacao_bp', __name__)

# Configuração para upload de arquivos
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'uploads')
ALLOWED_EXTENSIONS = {'xml', 'txt'}

# Criar diretório de uploads se não existir
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

def allowed_file(filename):
    """
    Verifica se o arquivo tem uma extensão permitida
    """
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@importacao_bp.route('/api/importacoes/batch', methods=['POST'])
@jwt_required()
def importar_xml_batch():
    """
    Importa múltiplos arquivos XML em lote
    """
    try:
        print("[BATCH] Iniciando importação em lote...")
        print(f"[BATCH] Headers da requisição: {dict(request.headers)}")
        print(f"[BATCH] Files na requisição: {request.files.keys()}")

        # Verificar se foram enviados arquivos
        if 'arquivos' not in request.files:
            print("[BATCH] Erro: Campo 'arquivos' não encontrado na requisição")
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivos = request.files.getlist('arquivos')
        print(f"[BATCH] Número de arquivos recebidos: {len(arquivos)}")
        print(f"[BATCH] Nomes dos arquivos: {[a.filename for a in arquivos]}")

        if not arquivos or all(arquivo.filename == '' for arquivo in arquivos):
            print("[BATCH] Erro: Nenhum arquivo válido selecionado")
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Validar e processar arquivos
        xml_files = []
        invalid_files = []

        for arquivo in arquivos:
            if not arquivo.filename.endswith('.xml'):
                invalid_files.append({
                    'filename': arquivo.filename,
                    'message': 'Arquivo não é um XML'
                })
                continue

            try:
                # Ler conteúdo do arquivo como string
                xml_content = arquivo.read().decode('utf-8')
                if not xml_content:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'message': 'Arquivo vazio'
                    })
                    continue

                # Validar XML
                processor = XMLProcessor(xml_content)
                emitente = processor.get_emitente()
                cnpj_emitente = emitente.get('cnpj')

                if not cnpj_emitente:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': 'CNPJ do emitente não encontrado'
                    })
                    continue

                # Verificar se empresa existe
                empresa = Empresa.query.filter_by(cnpj=cnpj_emitente).first()
                if not empresa:
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': f'Empresa não cadastrada (CNPJ: {cnpj_emitente})'
                    })
                    continue

                # Verificar permissões para a empresa
                if not (usuario.is_admin or
                        usuario.tipo_usuario == 'admin' or
                        (usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id) or
                        (usuario.empresas_permitidas and empresa.id in usuario.empresas_permitidas)):
                    invalid_files.append({
                        'filename': arquivo.filename,
                        'reason': f'Sem permissão para importar para empresa {empresa.razao_social}'
                    })
                    continue

                # Adicionar à lista de XMLs válidos
                xml_files.append({
                    'filename': arquivo.filename,
                    'content': xml_content
                })

            except Exception as e:
                invalid_files.append({
                    'filename': arquivo.filename,
                    'reason': f'Erro ao processar XML: {str(e)}'
                })
                continue

        if not xml_files:
            return jsonify({
                "message": "Nenhum XML válido para importação",
                "invalid_files": invalid_files
            }), 400

        # Processar XMLs válidos em lote
        # Determinar o escritório_id com base no tipo de usuário e permissões
        if usuario.tipo_usuario == 'escritorio':
            escritorio_id = usuario.escritorio_id
        elif usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Admin pode importar para qualquer escritório, então usamos None
            escritorio_id = None
        else:
            # Para usuários comuns, pegar o escritório da primeira empresa válida
            if len(xml_files) > 0:
                primeiro_xml = xml_files[0]
                processor = XMLProcessor(primeiro_xml['content'])
                emitente = processor.get_emitente()
                cnpj_emitente = emitente.get('cnpj')
                empresa = Empresa.query.filter_by(cnpj=cnpj_emitente).first()
                escritorio_id = empresa.escritorio_id if empresa else None
            else:
                escritorio_id = None

        # Gerar ID único para esta importação
        import uuid
        import_id = str(uuid.uuid4())

        # Obter serviço WebSocket
        from services.websocket_service import get_websocket_service
        websocket_service = get_websocket_service()

        # Criar callback de progresso
        progress_callback = None
        if websocket_service:
            progress_callback = websocket_service.create_progress_callback(import_id)

        # Importar sistema de filas
        from services.queue_manager import get_queue_manager, Task, TaskStatus
        queue_manager = get_queue_manager()

        # Criar tarefa para a fila
        task = Task(
            id=import_id,
            type='batch_import',
            user_id=usuario_id,
            empresa_id=0,  # Múltiplas empresas
            data={
                'xml_files': xml_files,
                'escritorio_id': escritorio_id,
                'usuario_id': usuario_id,
                'progress_callback': progress_callback
            },
            priority=1  # Prioridade normal
        )

        # Definir callback para processamento
        def process_batch_import(data):
            service = BatchXMLImportService(
                escritorio_id=data['escritorio_id'],
                usuario_id=data['usuario_id'],
                max_workers=2,  # Reduzido para evitar sobrecarga
                progress_callback=data['progress_callback']
            )
            return service.process_xml_batch(data['xml_files'])

        task.callback = process_batch_import

        # Submeter tarefa para a fila
        if not queue_manager.submit_import_task(task):
            return jsonify({
                "message": "Sistema sobrecarregado. Tente novamente em alguns minutos.",
                "error": "Queue full"
            }), 503  # Service Unavailable

        # O processamento será feito pela fila em background
        # WebSocket notificará automaticamente sobre o progresso

        # Retornar imediatamente com o import_id
        return jsonify({
            "message": "Importação iniciada",
            "import_id": import_id,
            "total_files": len(xml_files),
            "invalid_files": invalid_files,
            "status": "processing"
        }), 202  # 202 Accepted - processamento iniciado

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"[BATCH] Erro ao processar lote de XMLs: {str(e)}")
        print(f"[BATCH] Stack trace completo:\n{error_trace}")

        # Enviar notificação de erro via WebSocket
        if 'websocket_service' in locals() and websocket_service and 'import_id' in locals():
            websocket_service.send_import_error(import_id, {
                "message": "Erro ao processar solicitação",
                "error": str(e)
            })

        return jsonify({
            "message": "Erro ao processar solicitação",
            "error": str(e),
            "stack_trace": error_trace
        }), 500

@importacao_bp.route('/api/importacoes', methods=['GET'])
@jwt_required()
def listar_importacoes():
    """
    Lista as importações de XML realizadas
    """
    try:
        # Obter parâmetros de filtro
        empresa_id = request.args.get('empresa_id', type=int)

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir a query base
        query = ImportacaoXML.query

        # Filtrar por empresa se especificado
        if empresa_id:
            query = query.filter_by(empresa_id=empresa_id)

        # Aplicar filtros com base no tipo de usuário
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores veem todas as importações
            pass
        elif usuario.tipo_usuario == 'escritorio':
            # Usuários do tipo escritório veem importações do seu escritório
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        else:
            # Usuários comuns veem apenas importações das empresas permitidas
            empresas_permitidas = usuario.empresas_permitidas or []
            query = query.filter(ImportacaoXML.empresa_id.in_(empresas_permitidas))

        # Ordenar por data de importação (mais recentes primeiro)
        query = query.order_by(ImportacaoXML.data_importacao.desc())

        # Executar a query
        importacoes = query.all()

        return jsonify({
            "importacoes": [importacao.to_dict() for importacao in importacoes]
        }), 200

    except Exception as e:
        print(f"Erro ao listar importações: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes/<int:importacao_id>', methods=['GET'])
@jwt_required()
def obter_importacao(importacao_id):
    """
    Obtém os detalhes de uma importação específica
    """
    try:
        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Buscar a importação
        importacao = db.session.get(ImportacaoXML, importacao_id)

        if not importacao:
            return jsonify({"message": "Importação não encontrada"}), 404

        # Verificar permissões para visualizar a importação
        if usuario.is_admin or usuario.tipo_usuario == 'admin':
            # Administradores podem ver qualquer importação
            pass
        elif usuario.tipo_usuario == 'escritorio' and importacao.escritorio_id == usuario.escritorio_id:
            # Usuários do tipo escritório só podem ver importações do seu próprio escritório
            pass
        elif usuario.empresas_permitidas and importacao.empresa_id in usuario.empresas_permitidas:
            # Usuários comuns só podem ver importações das empresas permitidas
            pass
        else:
            return jsonify({"message": "Você não tem permissão para visualizar esta importação"}), 403

        return jsonify({
            "importacao": importacao.to_dict()
        }), 200

    except Exception as e:
        print(f"Erro ao obter importação: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500

@importacao_bp.route('/api/importacoes', methods=['POST'])
@jwt_required()
def importar_xml():
    """
    Importa um arquivo XML
    """
    try:
        # Verificar se há arquivo na requisição
        if 'arquivo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivo = request.files['arquivo']

        # Verificar se o arquivo tem nome
        if arquivo.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar se o arquivo é um XML
        if not allowed_file(arquivo.filename):
            return jsonify({"message": "Formato de arquivo não permitido. Use XML"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Salvar o arquivo temporariamente
        filename = secure_filename(arquivo.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        arquivo.save(filepath)

        # Ler o conteúdo do arquivo
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                xml_content = f.read()
        except UnicodeDecodeError:
            # Tentar com outra codificação se UTF-8 falhar
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    xml_content = f.read()
            except Exception as e:
                return jsonify({"message": f"Erro ao ler o arquivo XML: {str(e)}"}), 400

        # Extrair o CNPJ do emitente do XML
        try:
            processor = XMLProcessor(xml_content)
            emitente = processor.get_emitente()
            cnpj_emitente = emitente.get('cnpj')

            if not cnpj_emitente:
                return jsonify({"message": "Não foi possível extrair o CNPJ do emitente do XML"}), 400

            print(f"CNPJ do emitente extraído do XML: {cnpj_emitente}")

            # Buscar empresa pelo CNPJ
            empresa = Empresa.query.filter_by(cnpj=cnpj_emitente).first()

            if not empresa:
                return jsonify({
                    "message": f"Não foi encontrada nenhuma empresa cadastrada com o CNPJ {cnpj_emitente}. Por favor, cadastre a empresa antes de importar o XML."
                }), 400

            print(f"Empresa encontrada: {empresa.razao_social} (ID: {empresa.id})")

            # Verificar permissões para importar para esta empresa
            if usuario.is_admin or usuario.tipo_usuario == 'admin':
                # Administradores podem importar para qualquer empresa
                pass
            elif usuario.tipo_usuario == 'escritorio' and empresa.escritorio_id == usuario.escritorio_id:
                # Usuários do tipo escritório só podem importar para empresas do seu próprio escritório
                pass
            elif usuario.empresas_permitidas and empresa.id in usuario.empresas_permitidas:
                # Usuários comuns só podem importar para empresas permitidas
                pass
            else:
                return jsonify({"message": f"Você não tem permissão para importar para a empresa {empresa.razao_social} (CNPJ: {empresa.cnpj})"}), 403

        except Exception as e:
            return jsonify({"message": f"Erro ao processar o XML: {str(e)}"}), 400

        # Importar o XML
        service = XMLImportService(
            empresa_id=empresa.id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        resultado = service.import_xml(xml_content, filename)

        # Remover o arquivo temporário
        try:
            os.remove(filepath)
        except:
            pass

        if resultado['success']:
            return jsonify({
                "message": "XML importado com sucesso",
                "importacao": resultado
            }), 201
        else:
            return jsonify({
                "message": resultado['message'],
                "error": resultado.get('error'),
                "stack_trace": resultado.get('stack_trace')
            }), 400

    except Exception as e:
        print(f"Erro ao importar XML: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500


@importacao_bp.route('/api/importacoes/sped', methods=['POST'])
@jwt_required()
def importar_sped():
    """
    Importa um arquivo SPED
    """
    try:
        # Verificar se há arquivo na requisição
        if 'arquivo' not in request.files:
            return jsonify({"message": "Nenhum arquivo enviado"}), 400

        arquivo = request.files['arquivo']

        # Verificar se o arquivo tem nome
        if arquivo.filename == '':
            return jsonify({"message": "Nenhum arquivo selecionado"}), 400

        # Verificar se o arquivo é um TXT
        if not arquivo.filename.lower().endswith('.txt'):
            return jsonify({"message": "Formato de arquivo não permitido. Use TXT"}), 400

        # Verificar permissões
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Salvar o arquivo temporariamente
        filename = secure_filename(arquivo.filename)
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        arquivo.save(filepath)

        # Ler o conteúdo do arquivo
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                sped_content = f.read()
        except UnicodeDecodeError:
            # Tentar com outra codificação se UTF-8 falhar
            try:
                with open(filepath, 'r', encoding='latin-1') as f:
                    sped_content = f.read()
            except Exception as e:
                return jsonify({"message": f"Erro ao ler o arquivo SPED: {str(e)}"}), 400

        # Processar o SPED para obter informações da empresa
        try:
            processor = SPEDProcessor(sped_content)
            empresa_data = processor.get_empresa_data()
            cnpj_empresa = empresa_data.get('cnpj')

            if not cnpj_empresa:
                return jsonify({"message": "CNPJ da empresa não encontrado no arquivo SPED"}), 400

        except Exception as e:
            return jsonify({"message": f"Erro ao processar o SPED: {str(e)}"}), 400

        # Verificar se empresa existe
        empresa = Empresa.query.filter_by(cnpj=cnpj_empresa).first()
        if not empresa:
            return jsonify({
                "message": f"Empresa não cadastrada (CNPJ: {cnpj_empresa})"
            }), 400

        # Verificar permissões do usuário para a empresa
        if usuario.tipo_usuario == 'empresa' and usuario.empresa_id != empresa.id:
            return jsonify({"message": "Sem permissão para importar para esta empresa"}), 403

        if usuario.tipo_usuario == 'escritorio' and usuario.escritorio_id != empresa.escritorio_id:
            return jsonify({"message": "Sem permissão para importar para esta empresa"}), 403

        # Importar o SPED
        service = SPEDImportService(
            empresa_id=empresa.id,
            escritorio_id=empresa.escritorio_id,
            usuario_id=usuario_id
        )

        resultado = service.import_sped(sped_content, filename)

        # Remover o arquivo temporário
        try:
            os.remove(filepath)
        except:
            pass

        if resultado['success']:
            return jsonify({
                "message": "SPED importado com sucesso",
                "importacao": resultado
            }), 201
        else:
            return jsonify({
                "message": resultado['message'],
                "error": resultado.get('error'),
                "stack_trace": resultado.get('stack_trace')
            }), 400

    except Exception as e:
        print(f"Erro ao importar SPED: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500


@importacao_bp.route('/api/importacoes/sped/historico', methods=['GET'])
@jwt_required()
def listar_importacoes_sped():
    """
    Lista o histórico de importações SPED
    """
    try:
        usuario_id = get_jwt_identity()
        usuario = db.session.get(Usuario, usuario_id)

        if not usuario:
            return jsonify({"message": "Usuário não encontrado"}), 404

        # Construir query baseada no tipo de usuário
        query = ImportacaoSped.query

        if usuario.tipo_usuario == 'empresa':
            query = query.filter_by(empresa_id=usuario.empresa_id)
        elif usuario.tipo_usuario == 'escritorio':
            query = query.filter_by(escritorio_id=usuario.escritorio_id)
        # Admin pode ver todas

        # Ordenar por data de importação (mais recentes primeiro)
        importacoes = query.order_by(ImportacaoSped.data_importacao.desc()).all()

        return jsonify({
            "importacoes": [importacao.to_dict() for importacao in importacoes]
        }), 200

    except Exception as e:
        print(f"Erro ao listar importações SPED: {str(e)}")
        return jsonify({"message": "Erro ao processar solicitação"}), 500
