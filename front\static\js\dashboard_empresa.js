/**
 * Dashboard Empresa - Auditoria Fiscal
 * Dashboard específico para uma empresa com cards por tributo
 */

// Variáveis globais
let empresaDashboardData = null;
let empresaId = null;
let graficosData = null;
let chartInstances = {};

/**
 * Inicializa o dashboard da empresa
 */
function initDashboardEmpresa() {
  // Extrair ID da empresa da URL
  const pathParts = window.location.pathname.split('/');

  if (
    pathParts.length >= 4 &&
    pathParts[1] === 'dashboard' &&
    pathParts[2] === 'empresa'
  ) {
    empresaId = parseInt(pathParts[3]);

    if (isNaN(empresaId)) {
      mostrarErroDashboardEmpresa('ID da empresa inválido');
      return;
    }

    // Adicionar classe para ocultar elementos do dashboard geral
    document.body.classList.add('dashboard-empresa-page');

    // Carregar dados do dashboard da empresa
    carregarDashboardEmpresa();

    // Configurar event listeners
    setupFiltrosDashboardEmpresa();
    setupBreadcrumb();
  } else {
    // Remover classe se não estivermos na página de empresa
    document.body.classList.remove('dashboard-empresa-page');
  }
}

/**
 * Configura o breadcrumb de navegação
 */
function setupBreadcrumb() {
  // Adicionar breadcrumb se não existir
  const breadcrumbContainer = document.querySelector('.breadcrumb-container');
  if (!breadcrumbContainer) {
    const header = document.querySelector('.section-header');
    if (header) {
      const breadcrumb = document.createElement('nav');
      breadcrumb.className = 'breadcrumb-container mb-3';
      breadcrumb.innerHTML = `
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="/dashboard" class="text-decoration-none">
                            <i class="fas fa-home"></i> Dashboard
                        </a>
                    </li>
                    <li class="breadcrumb-item active" id="breadcrumb-empresa">
                        <i class="fas fa-building"></i> Carregando...
                    </li>
                </ol>
            `;
      header.appendChild(breadcrumb);
    }
  }
}

/**
 * Configura os filtros do dashboard da empresa
 */
function setupFiltrosDashboardEmpresa() {
  // Event listener para mudança de ano
  const yearSelect = document.getElementById('year-select');
  if (yearSelect) {
    yearSelect.addEventListener('change', function () {
      carregarDashboardEmpresa();
    });
  }

  // Event listener para mudança de mês
  const monthSelect = document.getElementById('month-select');
  if (monthSelect) {
    monthSelect.addEventListener('change', function () {
      carregarDashboardEmpresa();
    });
  }
}

/**
 * Carrega os dados do dashboard da empresa
 */
function carregarDashboardEmpresa() {
  if (!empresaId) {
    return;
  }

  const year =
    document.getElementById('year-select')?.value || new Date().getFullYear();
  const month =
    document.getElementById('month-select')?.value || new Date().getMonth() + 1;

  // Mostrar loading
  mostrarLoadingDashboardEmpresa();

  // Fazer requisição para a API
  const url = `/api/dashboard/empresa/${empresaId}?year=${year}&month=${month}`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response.json();
    })
    .then((data) => {
      if (data.success) {
        empresaDashboardData = data;
        atualizarDashboardEmpresa(data);
      } else {
        mostrarErroDashboardEmpresa(data.message || 'Erro desconhecido');
      }
    })
    .catch((error) => {
      mostrarErroDashboardEmpresa(`Erro de conexão: ${error.message}`);
    });
}

/**
 * Atualiza o dashboard da empresa com os dados recebidos
 */
function atualizarDashboardEmpresa(data) {
  // Atualizar breadcrumb
  const breadcrumbEmpresa = document.getElementById('breadcrumb-empresa');
  if (breadcrumbEmpresa) {
    breadcrumbEmpresa.innerHTML = `<i class="fas fa-building"></i> ${data.empresa.razao_social}`;
  }

  // Atualizar título da página
  const pageTitle = document.querySelector('.section-header h2');
  if (pageTitle) {
    pageTitle.innerHTML = `<i class="fas fa-chart-line"></i> Dashboard - ${data.empresa.razao_social}`;
  }

  // Criar container principal se não existir
  let mainContainer = document.getElementById('dashboard-empresa-main');
  if (!mainContainer) {
    const pageSection = document.querySelector('.page-section.active');
    if (pageSection) {
      mainContainer = document.createElement('div');
      mainContainer.id = 'dashboard-empresa-main';
      mainContainer.className = 'dashboard-empresa-main';
      pageSection.appendChild(mainContainer);
    }
  }

  if (mainContainer) {
    // LIMPAR O LOADING PRIMEIRO
    mainContainer.innerHTML = '';

    // Criar sistema de abas
    criarSistemaAbas(mainContainer);

    // Gerar conteúdo da aba resumo
    gerarConteudoAbaResumo(data);
  }
}

/**
 * Cria o sistema de abas para o dashboard da empresa
 */
function criarSistemaAbas(container) {
  const abasHTML = `
    <div class="dashboard-empresa-tabs">
      <!-- Nav tabs -->
      <ul class="nav nav-tabs" id="dashboardEmpresaTabs" role="tablist">
        <li class="nav-item" role="presentation">
          <button class="nav-link active" id="resumo-tab" data-bs-toggle="tab" data-bs-target="#resumo"
                  type="button" role="tab" aria-controls="resumo" aria-selected="true">
            <i class="fas fa-chart-bar"></i> Resumo
          </button>
        </li>
        <li class="nav-item" role="presentation">
          <button class="nav-link" id="graficos-tab" data-bs-toggle="tab" data-bs-target="#graficos"
                  type="button" role="tab" aria-controls="graficos" aria-selected="false">
            <i class="fas fa-chart-line"></i> Gráficos
          </button>
        </li>
      </ul>

      <!-- Tab panes -->
      <div class="tab-content" id="dashboardEmpresaTabContent">
        <div class="tab-pane fade show active" id="resumo" role="tabpanel" aria-labelledby="resumo-tab">
          <div id="dashboard-empresa-cards" class="dashboard-empresa-cards mt-3">
            <!-- Conteúdo do resumo será carregado aqui -->
          </div>
        </div>
        <div class="tab-pane fade" id="graficos" role="tabpanel" aria-labelledby="graficos-tab">
          <div id="dashboard-empresa-graficos" class="dashboard-empresa-graficos mt-3">
            <!-- Conteúdo dos gráficos será carregado aqui -->
          </div>
        </div>
      </div>
    </div>
  `;

  container.innerHTML = abasHTML;

  // Configurar event listeners para as abas
  const graficosTab = document.getElementById('graficos-tab');
  if (graficosTab) {
    graficosTab.addEventListener('shown.bs.tab', function (event) {
      // Carregar gráficos quando a aba for ativada
      carregarGraficosEmpresa();
    });
  }
}

/**
 * Gera o conteúdo da aba resumo
 */
function gerarConteudoAbaResumo(data) {
  const cardsContainer = document.getElementById('dashboard-empresa-cards');
  if (cardsContainer) {
    // Gerar resumo geral primeiro
    gerarResumoGeral(cardsContainer, data.total_geral);

    // Gerar cards dos tributos depois
    gerarCardsTributos(cardsContainer, data.cards_tributos);

    // Adicionar botão de relatório geral
    adicionarBotaoRelatorioGeral(cardsContainer);
  }
}

/**
 * Gera os cards dos tributos
 */
function gerarCardsTributos(container, cardsTributos) {
  let html = `
        <div class="row mb-3">
            <div class="col-12">
                <h4><i class="fas fa-calculator"></i> Tributos</h4>
                <p class="tributos-help-text">Clique em um card para acessar a auditoria específica do tributo</p>
            </div>
        </div>
        <div class="row">
    `;

  cardsTributos.forEach((card) => {
    const statusClass = card.auditado ? 'border-success' : 'border-warning';
    const statusIcon = card.auditado
      ? 'fas fa-check-circle text-success'
      : 'fas fa-clock text-warning';
    const statusText = card.auditado ? 'Auditado' : 'Pendente';

    html += `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card tributo-card ${statusClass}"
                     ${
                       card.status_tipo !== 'nao_aplicavel'
                         ? `onclick="navegarParaAuditoriaTributo('${card.tipo_tributo}')"`
                         : ''
                     }
                     style="${
                       card.status_tipo !== 'nao_aplicavel'
                         ? 'cursor: pointer;'
                         : 'cursor: default;'
                     } transition: transform 0.2s;">
                    <div class="card-body py-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title mb-0">${
                              card.nome_tributo
                            }</h6>
                            <i class="${statusIcon}"></i>
                        </div>

                        <div class="mb-2">
                            <span class="badge ${
                              card.auditado ? 'bg-success' : 'bg-warning'
                            }">${statusText}</span>
                        </div>

                        ${
                          card.auditado
                            ? card.status_tipo === 'nao_aplicavel'
                              ? `
                                <div class="text-center py-2">
                                    <i class="fas fa-ban text-secondary fa-lg mb-2"></i>
                                    <p class="text-muted mb-1 small"><strong>Não Aplicável</strong></p>
                                    <p class="text-muted mb-0" style="font-size: 0.75rem;">${
                                      card.status_manual?.motivo ||
                                      'Empresa não possui operações deste tributo'
                                    }</p>
                                    <button class="btn btn-outline-primary btn-sm mt-2"
                                            onclick="event.stopPropagation(); alterarStatusManual('${
                                              card.tipo_tributo
                                            }', 'aplicavel')">
                                        <i class="fas fa-undo"></i> Reverter
                                    </button>
                                </div>
                              `
                              : `
                                <div class="row text-center mb-2">
                                    <div class="col-6">
                                        <small class="tributo-label">Notas</small>
                                        <div class="fw-bold tributo-value">${
                                          card.total_notas
                                        }</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="tributo-label">Itens</small>
                                        <div class="fw-bold tributo-value">${
                                          card.total_produtos
                                        }</div>
                                    </div>
                                </div>

                                <div class="row text-center mb-2">
                                    <div class="col-6">
                                        <small class="text-success">Conformes</small>
                                        <div class="fw-bold text-success tributo-value">${
                                          card.total_conforme
                                        }</div>
                                    </div>
                                    <div class="col-6">
                                        <small class="text-danger">Inconsistentes</small>
                                        <div class="fw-bold text-danger tributo-value">${
                                          card.total_inconsistente
                                        }</div>
                                    </div>
                                </div>

                                ${
                                  card.total_inconsistente > 0
                                    ? `
                                    <div class="text-center">
                                        <small class="tributo-label">Valor Inconsistente</small>
                                        <div class="fw-bold text-danger tributo-value">
                                            ${formatCurrency(
                                              card.valor_inconsistente_maior +
                                                card.valor_inconsistente_menor,
                                            )}
                                        </div>
                                    </div>
                                `
                                    : ''
                                }
                              `
                            : `
                            <div class="text-center py-2">
                                <i class="fas fa-exclamation-triangle text-warning fa-lg mb-1"></i>
                                <p class="text-muted mb-2 small">Auditoria não realizada</p>
                                <button class="btn btn-outline-secondary btn-sm"
                                        onclick="event.stopPropagation(); marcarComoNaoAplicavel('${card.tipo_tributo}')">
                                    <i class="fas fa-ban"></i> Marcar como Não Aplicável
                                </button>
                            </div>
                        `
                        }
                    </div>
                </div>
            </div>
        `;
  });

  html += '</div>';

  // Adicionar ao container
  const existingCards = container.querySelector('.tributos-cards');
  if (existingCards) {
    existingCards.innerHTML = html;
  } else {
    const div = document.createElement('div');
    div.className = 'tributos-cards';
    div.innerHTML = html;
    container.appendChild(div);
  }
}

/**
 * Gera o resumo geral
 */
function gerarResumoGeral(container, totalGeral) {
  const html = `
        <div class="row mb-3">
            <div class="col-12">
                <h4><i class="fas fa-chart-bar"></i> Resumo Geral</h4>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center py-3">
                        <h6 class="mb-2">Tributos Auditados</h6>
                        <h3 class="mb-0">${totalGeral.tributos_auditados}/6</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center py-3">
                        <h6 class="mb-2">Total de Notas</h6>
                        <h3 class="mb-0">${totalGeral.total_notas}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center py-3">
                        <h6 class="mb-2">Itens Conformes</h6>
                        <h3 class="mb-0">${totalGeral.total_conforme}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center py-3">
                        <h6 class="mb-2">Itens Inconsistentes</h6>
                        <h3 class="mb-0">${totalGeral.total_inconsistente}</h3>
                    </div>
                </div>
            </div>
        </div>
    `;

  // Adicionar ao container
  const existingResumo = container.querySelector('.resumo-geral');
  if (existingResumo) {
    existingResumo.innerHTML = html;
  } else {
    const div = document.createElement('div');
    div.className = 'resumo-geral';
    div.innerHTML = html;
    container.appendChild(div);
  }
}

/**
 * Navega para a auditoria específica do tributo
 */
function navegarParaAuditoriaTributo(tipoTributo) {
  // Determinar direção (entrada/saída) - por padrão usar entrada
  const direcao = 'entrada';

  // Ajustar nome do tributo para URL
  const tributoUrl = tipoTributo.replace('_', '-');

  // Navegar para a página de auditoria
  window.location.href = `/auditoria/${direcao}/${tributoUrl}`;
}

/**
 * Adiciona botão de relatório geral ao dashboard da empresa
 */
function adicionarBotaoRelatorioGeral(container) {
  // Verificar se já existe o botão
  if (document.getElementById('relatorio-geral-empresa')) {
    return;
  }

  const relatorioSection = document.createElement('div');
  relatorioSection.id = 'relatorio-geral-empresa';
  relatorioSection.className = 'row mt-4';
  relatorioSection.innerHTML = `
    <div class="col-12">
      <div class="card relatorio-geral-card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="fas fa-file-pdf"></i>
            Relatório Geral de Auditoria
          </h5>
        </div>
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-md-8">
              <p class="card-text">
                <strong>Relatório Consolidado</strong> - Inclui todos os tipos de tributos auditados para esta empresa.
              </p>
              <p class="small">
                <i class="fas fa-info-circle me-1"></i>
                O relatório será gerado em formato PDF com logo do escritório e dados completos da auditoria.
              </p>
            </div>
            <div class="col-md-4 text-end">
              <button type="button" class="btn btn-primary btn-lg" onclick="gerarRelatorioGeralEmpresa()">
                <i class="fas fa-download"></i> Baixar Relatório Geral
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  container.appendChild(relatorioSection);
}

/**
 * Gera relatório geral para a empresa atual
 */
function gerarRelatorioGeralEmpresa() {
  const empresaId = localStorage.getItem('selectedCompany');
  const year = localStorage.getItem('selectedYear');
  const month = localStorage.getItem('selectedMonth');

  if (!empresaId) {
    alert(
      'Erro: ID da empresa não encontrado. Recarregue a página e tente novamente.',
    );
    return;
  }

  // Construir URL com parâmetros
  const params = new URLSearchParams({
    empresa_id: empresaId,
    status: 'inconsistente', // Por padrão, mostrar apenas inconsistências
  });

  if (year && month) {
    params.append('year', year);
    params.append('month', month);
  }

  const url = `/api/relatorios/geral?${params.toString()}`;

  // Encontrar o botão
  const btn = document.querySelector(
    'button[onclick="gerarRelatorioGeralEmpresa()"]',
  );
  if (btn) {
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Gerando...';
    btn.disabled = true;

    // Restaurar botão após um tempo
    setTimeout(() => {
      btn.innerHTML = originalText;
      btn.disabled = false;
    }, 2000);
  }

  // Fazer download com autenticação
  downloadRelatorioComAuth(url, `relatorio_geral_${empresaId}.pdf`);
}

/**
 * Faz download de relatório com autenticação
 * @param {string} url - URL do relatório
 * @param {string} filename - Nome do arquivo
 */
function downloadRelatorioComAuth(url, filename) {
  const token = localStorage.getItem('token');

  if (!token) {
    alert('Token de autenticação não encontrado. Faça login novamente.');
    return;
  }

  // Fazer requisição com fetch para incluir headers de autenticação
  fetch(url, {
    method: 'GET',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`Erro ${response.status}: ${response.statusText}`);
      }
      return response.blob();
    })
    .then((blob) => {
      // Criar URL temporária para o blob
      const blobUrl = window.URL.createObjectURL(blob);

      // Criar link temporário para download
      const link = document.createElement('a');
      link.href = blobUrl;
      link.download = filename;
      link.style.display = 'none';

      // Adicionar ao DOM, clicar e remover
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Limpar URL temporária
      window.URL.revokeObjectURL(blobUrl);
    })
    .catch((error) => {
      alert(`Erro ao baixar relatório: ${error.message}`);
    });
}

/**
 * Funções auxiliares
 */
function formatCurrency(value) {
  if (!value || value === 0) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Funções de loading e erro
 */
function mostrarLoadingDashboardEmpresa() {
  const pageSection = document.querySelector('.page-section.active');
  if (pageSection) {
    // Verificar se já existe o container principal
    let mainContainer = document.getElementById('dashboard-empresa-main');
    if (!mainContainer) {
      mainContainer = document.createElement('div');
      mainContainer.id = 'dashboard-empresa-main';
      mainContainer.className = 'dashboard-empresa-main';
      pageSection.appendChild(mainContainer);
    }

    mainContainer.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-spinner fa-spin fa-3x mb-3"></i>
                <h4>Carregando dashboard da empresa...</h4>
            </div>
        `;
  }
}

function mostrarErroDashboardEmpresa(message) {
  const pageSection = document.querySelector('.page-section.active');
  if (pageSection) {
    let cardsContainer = document.getElementById('dashboard-empresa-cards');
    if (!cardsContainer) {
      cardsContainer = document.createElement('div');
      cardsContainer.id = 'dashboard-empresa-cards';
      cardsContainer.className = 'dashboard-empresa-cards';
      pageSection.appendChild(cardsContainer);
    }

    cardsContainer.innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <h4>Erro ao carregar dashboard</h4>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="carregarDashboardEmpresa()">
                    <i class="fas fa-redo"></i> Tentar Novamente
                </button>
            </div>
        `;
  }
}

// Inicializar quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function () {
  // Aguardar um pouco para garantir que outros scripts foram carregados
  setTimeout(() => {
    initDashboardEmpresa();
  }, 500);
});

// Também inicializar quando a página for carregada completamente
window.addEventListener('load', function () {
  // Verificar se já foi inicializado, se não, inicializar
  if (!empresaId) {
    setTimeout(initDashboardEmpresa, 100);
  }
});

/**
 * Marca um tributo como não aplicável
 */
function marcarComoNaoAplicavel(tipoTributo) {
  // Mostrar modal para inserir motivo
  const motivo = prompt(
    `Por que o tributo ${tipoTributo.toUpperCase()} não se aplica a esta empresa?\n\n` +
      'Exemplos:\n' +
      '- Empresa não possui operações de ICMS-ST\n' +
      '- Não há vendas interestaduais (DIFAL)\n' +
      '- Empresa do Simples Nacional (IPI)',
    'Empresa não possui operações deste tributo',
  );

  if (motivo === null) return; // Usuário cancelou

  if (!motivo.trim()) {
    alert('Por favor, informe o motivo.');
    return;
  }

  alterarStatusManual(tipoTributo, 'nao_aplicavel', motivo.trim());
}

/**
 * Altera o status manual de um tributo
 */
function alterarStatusManual(tipoTributo, status, motivo = '') {
  if (!empresaId) {
    return;
  }

  // Confirmar ação
  const acao =
    status === 'nao_aplicavel'
      ? 'marcar como não aplicável'
      : 'reverter para aplicável';
  if (
    !confirm(
      `Tem certeza que deseja ${acao} o tributo ${tipoTributo.toUpperCase()}?`,
    )
  ) {
    return;
  }

  // Fazer requisição para a API
  fetch(`/api/dashboard/empresa/${empresaId}/status-manual`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
    body: JSON.stringify({
      tipo_tributo: tipoTributo,
      status: status,
      motivo: motivo,
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        // Mostrar mensagem de sucesso
        alert(data.message);

        // Recarregar dashboard
        carregarDashboardEmpresa();
      } else {
        alert(`Erro: ${data.message}`);
      }
    })
    .catch((error) => {
      alert('Erro de conexão. Tente novamente.');
    });
}

/**
 * Carrega os dados dos gráficos da empresa
 */
function carregarGraficosEmpresa() {
  if (!empresaId) {
    return;
  }

  // Verificar se já carregamos os dados
  if (graficosData) {
    renderizarGraficos();
    return;
  }

  // Mostrar loading
  mostrarLoadingGraficos();

  // Fazer requisição para a API
  const url = `/api/dashboard/empresa/${empresaId}/graficos`;

  fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  })
    .then((response) => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .then((data) => {
      if (data.success) {
        graficosData = data;
        renderizarGraficos();
      } else {
        mostrarErroGraficos(data.message || 'Erro desconhecido');
      }
    })
    .catch((error) => {
      mostrarErroGraficos(`Erro de conexão: ${error.message}`);
    });
}

/**
 * Mostra loading nos gráficos
 */
function mostrarLoadingGraficos() {
  const container = document.getElementById('dashboard-empresa-graficos');
  if (container) {
    container.innerHTML = `
      <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Carregando...</span>
        </div>
        <p class="mt-3">Carregando gráficos...</p>
      </div>
    `;
  }
}

/**
 * Mostra erro nos gráficos
 */
function mostrarErroGraficos(mensagem) {
  const container = document.getElementById('dashboard-empresa-graficos');
  if (container) {
    container.innerHTML = `
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Erro ao carregar gráficos: ${mensagem}
      </div>
    `;
  }
}

// Exportar funções para uso global
window.initDashboardEmpresa = initDashboardEmpresa;
window.navegarParaAuditoriaTributo = navegarParaAuditoriaTributo;
window.marcarComoNaoAplicavel = marcarComoNaoAplicavel;
window.alterarStatusManual = alterarStatusManual;
