/**
 * Componente de filtros dropdown com checkboxes para cenários
 * Implementa filtros cascata onde a seleção de um filtro afeta as opções dos outros
 */

// Variáveis globais para armazenar dados dos filtros
window.cenariosDropdownFilters = {
  opcoes: {},
  filtrosSelecionados: {
    cfops: [],
    ncms: [],
    csts: [],
    estados: [],
    aliquotas: [],
    reducoes: [],
  },
  isLoading: false,
};

/**
 * Carrega as opções de filtros do backend
 * @param {string} tipoTributo - Tipo de tributo
 * @param {number} empresaId - ID da empresa
 * @param {string} direcao - Direção (entrada/saida)
 * @param {number} year - Ano
 * @param {number} month - Mês
 * @param {string} status - Status dos cenários
 */
async function carregarOpcoesFiltros(
  tipoTributo,
  empresaId,
  direcao,
  year,
  month,
  status,
) {
  if (window.cenariosDropdownFilters.isLoading) {
    return;
  }

  window.cenariosDropdownFilters.isLoading = true;

  try {
    const params = new URLSearchParams();
    params.append('empresa_id', empresaId);
    params.append('direcao', direcao);
    params.append('status', status);

    if (year) params.append('year', year);
    if (month) params.append('month', month);

    const response = await fetch(
      `/api/cenarios/${tipoTributo}/filter-options?${params.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Erro na requisição: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();

    if (data.success) {
      window.cenariosDropdownFilters.opcoes = data.opcoes;
      console.log('Opções de filtros carregadas:', data.opcoes);
    } else {
      throw new Error(data.message || 'Erro ao carregar opções de filtros');
    }
  } catch (error) {
    console.error('Erro ao carregar opções de filtros:', error);
    // Em caso de erro, usar opções vazias
    window.cenariosDropdownFilters.opcoes = {
      cfops: [],
      ncms: [],
      csts: [],
      estados: [],
      aliquotas: [],
      reducoes: [],
    };
  } finally {
    window.cenariosDropdownFilters.isLoading = false;
  }
}

/**
 * Cria um dropdown com checkboxes para um tipo de filtro
 * @param {string} tipo - Tipo do filtro (cfops, ncms, csts, aliquotas)
 * @param {string} placeholder - Placeholder do input
 * @param {string} columnName - Nome da coluna para identificação
 * @param {number} originalIndex - Índice original da coluna
 * @returns {string} HTML do dropdown
 */
function criarDropdownFiltro(tipo, placeholder, columnName, originalIndex) {
  const dropdownId = `dropdown-${tipo}-${originalIndex}`;
  const inputId = `input-${tipo}-${originalIndex}`;
  const containerId = `container-${tipo}-${originalIndex}`;

  return `
    <div class="dropdown w-100">
      <input type="text"
             class="form-control form-control-sm dropdown-toggle column-filter-dropdown"
             id="${inputId}"
             data-bs-toggle="dropdown"
             data-bs-auto-close="outside"
             data-column="${originalIndex}"
             data-column-name="${columnName}"
             data-original-index="${originalIndex}"
             data-filter-type="${tipo}"
             placeholder="${placeholder}"
             readonly
             style="cursor: pointer; font-size: 0.8rem;">

      <div class="dropdown-menu p-2" id="${dropdownId}" style="min-width: 250px; max-height: 300px; overflow-y: auto;">
        <div class="mb-2">
          <input type="text"
                 class="form-control form-control-sm"
                 id="busca-${tipo}-${originalIndex}"
                 placeholder="Buscar ${tipo}..."
                 onkeyup="filtrarOpcoesDropdown('${tipo}', ${originalIndex})">
        </div>

        <div class="mb-2">
          <div class="d-flex justify-content-between">
            <button type="button"
                    class="btn btn-sm btn-outline-primary"
                    onclick="selecionarTodosDropdown('${tipo}', ${originalIndex})">
              Todos
            </button>
            <button type="button"
                    class="btn btn-sm btn-outline-secondary"
                    onclick="limparSelecaoDropdown('${tipo}', ${originalIndex})">
              Limpar
            </button>
          </div>
        </div>

        <div id="${containerId}" class="checkbox-container">
          <!-- Checkboxes serão inseridos aqui -->
        </div>

        <div class="mt-2 pt-2 border-top">
          <button type="button"
                  class="btn btn-sm btn-success w-100"
                  onclick="aplicarFiltroDropdown('${tipo}', ${originalIndex})">
            Aplicar Filtro
          </button>
        </div>
      </div>
    </div>
  `;
}

/**
 * Popula um dropdown com as opções disponíveis
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function popularDropdown(tipo, originalIndex) {
  const containerId = `container-${tipo}-${originalIndex}`;
  const container = document.getElementById(containerId);

  if (!container) {
    console.warn(`Container ${containerId} não encontrado`);
    return;
  }

  const opcoes = window.cenariosDropdownFilters.opcoes[tipo] || [];
  const filtrosSelecionados =
    window.cenariosDropdownFilters.filtrosSelecionados;

  // Filtrar opções baseado nos filtros já selecionados
  const opcoesDisponiveis = filtrarOpcoesPorRelacionamentos(tipo, opcoes);

  container.innerHTML = '';

  if (opcoesDisponiveis.length === 0) {
    container.innerHTML =
      '<div class="text-muted small">Nenhuma opção disponível</div>';
    return;
  }

  opcoesDisponiveis.forEach((opcao) => {
    const checkboxId = `checkbox-${tipo}-${originalIndex}-${opcao.value.replace(
      /[^a-zA-Z0-9]/g,
      '-',
    )}`;
    const isChecked = filtrosSelecionados[tipo].includes(opcao.value);

    const checkboxHtml = `
      <div class="form-check opcao-checkbox" data-value="${opcao.value}">
        <input class="form-check-input"
               type="checkbox"
               id="${checkboxId}"
               value="${opcao.value}"
               ${isChecked ? 'checked' : ''}>
        <label class="form-check-label small" for="${checkboxId}">
          ${opcao.value}
        </label>
      </div>
    `;

    container.insertAdjacentHTML('beforeend', checkboxHtml);
  });
}

/**
 * Filtra opções baseado nos relacionamentos com outros filtros
 * @param {string} tipo - Tipo do filtro atual
 * @param {Array} opcoes - Opções originais
 * @returns {Array} Opções filtradas
 */
function filtrarOpcoesPorRelacionamentos(tipo, opcoes) {
  const filtrosSelecionados =
    window.cenariosDropdownFilters.filtrosSelecionados;

  // Se não há filtros selecionados, mostrar todas as opções
  const temFiltrosAtivos = Object.keys(filtrosSelecionados).some(
    (key) => key !== tipo && filtrosSelecionados[key].length > 0,
  );

  if (!temFiltrosAtivos) {
    return opcoes;
  }

  return opcoes.filter((opcao) => {
    // Verificar se esta opção é compatível com os filtros selecionados
    for (const [tipoFiltro, valoresSelecionados] of Object.entries(
      filtrosSelecionados,
    )) {
      if (tipoFiltro === tipo || valoresSelecionados.length === 0) {
        continue;
      }

      // Verificar se há interseção entre os valores relacionados e os selecionados
      const valoresRelacionados = opcao.related[tipoFiltro] || [];
      const temIntersecao = valoresSelecionados.some((valor) =>
        valoresRelacionados.includes(valor),
      );

      if (!temIntersecao) {
        return false;
      }
    }

    return true;
  });
}

/**
 * Filtra as opções do dropdown baseado na busca
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function filtrarOpcoesDropdown(tipo, originalIndex) {
  const buscaInput = document.getElementById(`busca-${tipo}-${originalIndex}`);
  const container = document.getElementById(
    `container-${tipo}-${originalIndex}`,
  );

  if (!buscaInput || !container) return;

  const termoBusca = buscaInput.value.toLowerCase();
  const checkboxes = container.querySelectorAll('.opcao-checkbox');

  checkboxes.forEach((checkbox) => {
    const valor = checkbox.dataset.value.toLowerCase();
    const shouldShow = valor.includes(termoBusca);
    checkbox.style.display = shouldShow ? 'block' : 'none';
  });
}

/**
 * Seleciona todas as opções visíveis do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function selecionarTodosDropdown(tipo, originalIndex) {
  const container = document.getElementById(
    `container-${tipo}-${originalIndex}`,
  );
  if (!container) return;

  const checkboxes = container.querySelectorAll(
    '.opcao-checkbox:not([style*="display: none"]) input[type="checkbox"]',
  );
  checkboxes.forEach((checkbox) => {
    checkbox.checked = true;
  });
}

/**
 * Limpa a seleção do dropdown
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function limparSelecaoDropdown(tipo, originalIndex) {
  const container = document.getElementById(
    `container-${tipo}-${originalIndex}`,
  );
  if (!container) return;

  const checkboxes = container.querySelectorAll('input[type="checkbox"]');
  checkboxes.forEach((checkbox) => {
    checkbox.checked = false;
  });
}

/**
 * Aplica o filtro selecionado
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 */
function aplicarFiltroDropdown(tipo, originalIndex) {
  const container = document.getElementById(
    `container-${tipo}-${originalIndex}`,
  );
  const input = document.getElementById(`input-${tipo}-${originalIndex}`);

  if (!container || !input) return;

  // Obter valores selecionados
  const checkboxes = container.querySelectorAll(
    'input[type="checkbox"]:checked',
  );
  const valoresSelecionados = Array.from(checkboxes).map((cb) => cb.value);

  // Atualizar estado global
  window.cenariosDropdownFilters.filtrosSelecionados[tipo] =
    valoresSelecionados;

  // Atualizar texto do input
  if (valoresSelecionados.length === 0) {
    input.value = '';
  } else if (valoresSelecionados.length === 1) {
    input.value = valoresSelecionados[0];
  } else {
    input.value = `${valoresSelecionados.length} selecionados`;
  }

  // Aplicar filtro na tabela
  aplicarFiltroNaTabela(tipo, originalIndex, valoresSelecionados);

  // Atualizar outros dropdowns baseado nos relacionamentos
  atualizarDropdownsRelacionados(tipo);

  // Fechar dropdown
  const dropdown = bootstrap.Dropdown.getInstance(input);
  if (dropdown) {
    dropdown.hide();
  }
}

/**
 * Aplica o filtro na tabela DataTables
 * @param {string} tipo - Tipo do filtro
 * @param {number} originalIndex - Índice original da coluna
 * @param {Array} valoresSelecionados - Valores selecionados para filtrar
 */
function aplicarFiltroNaTabela(tipo, originalIndex, valoresSelecionados) {
  const status = window.cenariosDetalhes.currentStatus || 'novo';
  const table = window.cenariosDetalhes.cenarioTables[status];

  if (!table) {
    console.warn(`Tabela não encontrada para status ${status}`);
    return;
  }

  // Ajustar índice para considerar coluna de checkbox na aba "Novos"
  const adjustedIndex = status === 'novo' ? originalIndex + 1 : originalIndex;

  // Verificar se a coluna existe
  const column = table.column(adjustedIndex);
  if (!column || !column.visible()) {
    console.warn(`Coluna ${adjustedIndex} não encontrada ou não visível`);
    return;
  }

  // Criar regex para filtro múltiplo
  let searchValue = '';
  if (valoresSelecionados.length > 0) {
    // Para filtros de alíquotas e reduções, considerar que os valores na tabela incluem "%"
    const isPercentageFilter = tipo === 'aliquotas' || tipo === 'reducoes';

    const escapedValues = valoresSelecionados.map((value) => {
      const escapedValue = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      // Para filtros percentuais, buscar pelo valor seguido de "%"
      return isPercentageFilter ? `${escapedValue}%` : escapedValue;
    });

    searchValue = `^(${escapedValues.join('|')})$`;
  }

  // Aplicar filtro
  column.search(searchValue, true, false).draw();
}

/**
 * Atualiza outros dropdowns baseado nos relacionamentos
 * @param {string} tipoAlterado - Tipo do filtro que foi alterado
 */
function atualizarDropdownsRelacionados(tipoAlterado) {
  const tiposParaAtualizar = [
    'cfops',
    'ncms',
    'csts',
    'estados',
    'aliquotas',
    'reducoes',
  ].filter((t) => t !== tipoAlterado);

  tiposParaAtualizar.forEach((tipo) => {
    // Encontrar todos os dropdowns deste tipo na página
    const dropdowns = document.querySelectorAll(`[data-filter-type="${tipo}"]`);

    dropdowns.forEach((dropdown) => {
      const originalIndex = parseInt(dropdown.dataset.originalIndex);
      if (!isNaN(originalIndex)) {
        popularDropdown(tipo, originalIndex);
      }
    });
  });
}

/**
 * Substitui um input de filtro de texto por um dropdown com checkboxes
 * @param {HTMLElement} input - Elemento input original
 * @param {string} tipo - Tipo do filtro (cfops, ncms, csts, aliquotas)
 */
function substituirInputPorDropdown(input, tipo) {
  const columnName = input.dataset.columnName;
  const originalIndex = parseInt(input.dataset.originalIndex);
  const placeholder = input.placeholder;

  // Criar dropdown
  const dropdownHtml = criarDropdownFiltro(
    tipo,
    placeholder,
    columnName,
    originalIndex,
  );

  // Substituir o input pelo dropdown
  input.outerHTML = dropdownHtml;

  // Popular o dropdown com as opções
  popularDropdown(tipo, originalIndex);
}

/**
 * Identifica o tipo de filtro baseado no nome da coluna
 * @param {string} columnName - Nome da coluna
 * @returns {string|null} Tipo do filtro ou null se não for um filtro dropdown
 */
function identificarTipoFiltro(columnName) {
  const mapeamento = {
    CFOP: 'cfops',
    NCM: 'ncms',
    CST: 'csts',
    Estado: 'estados',
    '% ICMS': 'aliquotas',
    '% ICMS ST': 'aliquotas',
    '% IPI': 'aliquotas',
    '% PIS': 'aliquotas',
    '% COFINS': 'aliquotas',
    '% DIFAL ICMS': 'aliquotas',
    '% FCP': 'aliquotas',
    '% Red. BC ICMS': 'reducoes',
    '% Red. BC ICMS ST': 'reducoes',
    '% Red. IPI': 'reducoes',
    '% Red. PIS': 'reducoes',
    '% Red. COFINS': 'reducoes',
  };

  return mapeamento[columnName] || null;
}

/**
 * Inicializa os filtros dropdown para uma tabela
 * @param {string} status - Status da tabela (novo, producao, inconsistente)
 */
async function inicializarFiltrosDropdown(status) {
  // Aguardar um pouco para garantir que a tabela esteja renderizada
  await new Promise((resolve) => setTimeout(resolve, 100));

  const tableContainer = document
    .querySelector(`#cenario-${status}-table`)
    ?.closest('.table-responsive');
  if (!tableContainer) {
    console.warn(`Container da tabela não encontrado para status ${status}`);
    return;
  }

  const filterInputs = tableContainer.querySelectorAll('input.column-filter');

  filterInputs.forEach((input) => {
    const columnName = input.dataset.columnName;
    const tipoFiltro = identificarTipoFiltro(columnName);

    if (tipoFiltro) {
      // Substituir input por dropdown
      substituirInputPorDropdown(input, tipoFiltro);
    }
  });
}

/**
 * Limpa todos os filtros dropdown
 */
function limparTodosFiltrosDropdown() {
  // Limpar estado global
  window.cenariosDropdownFilters.filtrosSelecionados = {
    cfops: [],
    ncms: [],
    csts: [],
    estados: [],
    aliquotas: [],
    reducoes: [],
  };

  // Limpar filtros na tabela
  const status = window.cenariosDetalhes.currentStatus || 'novo';
  const table = window.cenariosDetalhes.cenarioTables[status];

  if (table) {
    // Limpar todos os filtros de coluna
    table.columns().search('').draw();
  }

  // Atualizar todos os dropdowns
  ['cfops', 'ncms', 'csts', 'estados', 'aliquotas', 'reducoes'].forEach(
    (tipo) => {
      const dropdowns = document.querySelectorAll(
        `[data-filter-type="${tipo}"]`,
      );

      dropdowns.forEach((dropdown) => {
        const originalIndex = parseInt(dropdown.dataset.originalIndex);
        if (!isNaN(originalIndex)) {
          // Limpar texto do input
          dropdown.value = '';

          // Repopular dropdown
          popularDropdown(tipo, originalIndex);
        }
      });
    },
  );
}

/**
 * Obtém os valores atualmente selecionados em todos os filtros
 * @returns {Object} Objeto com os filtros selecionados
 */
function obterFiltrosSelecionados() {
  return { ...window.cenariosDropdownFilters.filtrosSelecionados };
}
