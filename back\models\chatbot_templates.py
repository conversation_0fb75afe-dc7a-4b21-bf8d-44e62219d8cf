"""
Modelo para templates de perguntas do chatbot IA
"""

from .escritorio import db
from sqlalchemy.sql import func

class ChatbotTemplates(db.Model):
    __tablename__ = 'chatbot_templates'
    
    id = db.Column(db.Integer, primary_key=True)
    categoria = db.Column(db.String(50), nullable=False)
    pergunta_template = db.Column(db.Text, nullable=False)
    sql_template = db.Column(db.Text, nullable=False)
    descricao = db.Column(db.Text, nullable=True)
    ativo = db.Column(db.<PERSON><PERSON>, default=True)
    data_criacao = db.Column(db.DateTime, server_default=func.now())
    
    def to_dict(self):
        """Converte o objeto para dicionário"""
        return {
            'id': self.id,
            'categoria': self.categoria,
            'pergunta_template': self.pergunta_template,
            'sql_template': self.sql_template,
            'descricao': self.descricao,
            'ativo': self.ativo,
            'data_criacao': self.data_criacao.isoformat() if self.data_criacao else None
        }
    
    def __repr__(self):
        return f'<ChatbotTemplates {self.id}: {self.categoria} - {self.pergunta_template[:30]}...>'
